[project]
name = "test-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "argon2-cffi>=25.1.0",
    "fastapi>=0.115.14",
    "langchain-google-genai>=2.1.6",
    "langchain-openai>=0.3.27",
    "langchain-qdrant>=0.2.0",
    "langgraph>=0.5.1",
    "langgraph-checkpoint-mongodb>=0.1.4",
    "passlib[bcrypt]>=1.7.4",
    "pyjwt>=2.10.1",
    "pymongo>=4.10,<4.13",
    "python-dotenv>=1.1.1",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "qdrant-client>=1.14.3",
    "scrapy>=2.13.3",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
