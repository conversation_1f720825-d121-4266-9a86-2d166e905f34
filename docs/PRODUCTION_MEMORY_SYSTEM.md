# 🧠 Production Memory System

## Overview

The Production Memory System provides permanent user sessions with structured data storage using MongoDBSaver and Pydantic models. This system ensures that user information is always accessible across conversations and never lost.

## 🎯 Key Features

### ✅ What This System Provides

1. **Permanent User Sessions**: Users maintain the same session across all conversations
2. **Structured Data Storage**: Pydantic models for type-safe data management
3. **Semantic Memory Search**: Vector-based search with embeddings
4. **Multiple Memory Types**: Categorized storage (personal_info, course_interest, etc.)
5. **Importance Levels**: Priority-based memory management
6. **Knowledge Triples**: Structured knowledge representation
7. **Profile Completeness**: Automatic tracking of user data completeness
8. **LLM-Agnostic**: Works with any LLM provider (Gemini, OpenAI, etc.)
9. **Production-Ready**: Error handling, indexes, and monitoring

### ❌ Problems This Solves

- **"Can't access previous conversation"** - Permanent sessions prevent data loss
- **Hardcoded user extraction** - Dynamic LLM-driven profile management
- **Session resets** - User ID-based permanent sessions
- **Unstructured memory** - Pydantic models ensure data consistency
- **Poor search** - Semantic search finds relevant information
- **Data loss** - MongoDB persistence with proper indexing

## 🏗️ Architecture

### Core Components

```
ProductionMemoryManager
├── UserProfile (Pydantic model)
├── StructuredMemory (Pydantic model)
├── ConversationSession (Pydantic model)
├── MongoDBSaver (checkpoints)
├── MongoDBStore (cross-thread memory)
└── Memory Tools (agent integration)
```

### Data Models

#### UserProfile
```python
class UserProfile(BaseModel):
    user_id: str
    name: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    education_level: Optional[str]
    course_interests: List[str]
    learning_goals: List[str]
    preferred_language: Optional[str]
    completed_courses: List[str]
    profile_completeness: float
    # ... and more fields
```

#### StructuredMemory
```python
class StructuredMemory(BaseModel):
    memory_id: str
    user_id: str
    content: str
    memory_type: MemoryType  # personal_info, course_interest, etc.
    importance: ImportanceLevel  # low, medium, high, critical
    knowledge_triples: List[KnowledgeTriple]
    embedding: Optional[List[float]]
    created_at: datetime
    access_count: int
```

#### ConversationSession
```python
class ConversationSession(BaseModel):
    session_id: str
    user_id: str
    thread_id: str
    started_at: datetime
    total_messages: int
    is_active: bool
    session_type: str
```

## 💻 Implementation

### Basic Usage

```python
from utils.production_memory_manager import get_production_memory_manager

# Get memory manager for tenant
memory_manager = get_production_memory_manager("tenant_id", "gemini")

# Get or create user profile
profile = memory_manager.get_or_create_user_profile("user_123")

# Update user profile
updates = {
    "name": "Ramesh Sharma",
    "email": "<EMAIL>",
    "course_interests": ["IELTS", "German"]
}
updated_profile = memory_manager.update_user_profile("user_123", updates)

# Save structured memory
memory = memory_manager.save_structured_memory(
    user_id="user_123",
    content="User wants to study IELTS for Canada immigration",
    memory_type=MemoryType.LEARNING_GOAL,
    importance=ImportanceLevel.HIGH
)

# Search memories
results = memory_manager.search_memories(
    user_id="user_123",
    query="IELTS course",
    limit=5
)

# Get permanent session
session = memory_manager.get_permanent_session("user_123")

# Get comprehensive user context
context = memory_manager.get_user_context("user_123", "current query")
```

### Agent Integration

```python
# Memory tools are automatically created and added to agents
tools = memory_manager.create_memory_tools()

# Tools available to agent:
# - save_user_memory: Save important information
# - search_user_memories: Find relevant memories
# - update_user_profile: Update profile fields
```

### Permanent Sessions

```python
# Users get permanent sessions based on user_id
user_id = str(current_user.user.id)

# Session is automatically created/retrieved
session = memory_manager.get_permanent_session(user_id)
thread_id = session.thread_id  # e.g., "permanent_user_123_abc12345"

# All conversations use the same thread_id
config = {"configurable": {"thread_id": thread_id, "user_id": user_id}}
```

## 🔧 Memory Types

### Available Memory Types

```python
class MemoryType(str, Enum):
    PERSONAL_INFO = "personal_info"        # Name, email, phone, etc.
    COURSE_INTEREST = "course_interest"    # Courses user is interested in
    LEARNING_GOAL = "learning_goal"        # User's learning objectives
    PREFERENCE = "preference"              # User preferences (schedule, etc.)
    FEEDBACK = "feedback"                  # User feedback about services
    QUESTION = "question"                  # Questions user has asked
    BOOKING_HISTORY = "booking_history"    # Past bookings and courses
    INTERACTION = "interaction"            # General interaction notes
```

### Importance Levels

```python
class ImportanceLevel(str, Enum):
    LOW = "low"           # General conversation notes
    MEDIUM = "medium"     # Preferences, interests
    HIGH = "high"         # Personal info, goals
    CRITICAL = "critical" # Essential user data
```

## 🔍 Search Capabilities

### Semantic Search
- Uses embeddings for similarity matching
- Finds relevant memories even with different wording
- Supports multiple LLM providers for embeddings

### Filtered Search
```python
# Search by memory type
results = memory_manager.search_memories(
    user_id="user_123",
    query="course information",
    memory_types=[MemoryType.COURSE_INTEREST, MemoryType.LEARNING_GOAL]
)

# Search by importance
results = memory_manager.search_memories(
    user_id="user_123",
    query="important information",
    min_importance=ImportanceLevel.HIGH
)
```

### Knowledge Triples
```python
# Structured knowledge representation
triple = KnowledgeTriple(
    subject="User",
    predicate="wants_to_study",
    object="IELTS",
    confidence=0.9
)

memory.add_knowledge_triple("User", "prefers", "weekend_classes", 0.8)
```

## 📊 Profile Management

### Profile Completeness
```python
# Automatic calculation of profile completeness
profile.calculate_completeness()  # Returns 0.0 to 1.0

# Weighted scoring:
# - Core fields (name, email, phone): 3x weight
# - Important fields (education, location): 2x weight
# - List fields (interests, goals): 1x weight
```

### Profile Updates
```python
# Single field update
memory_manager.update_user_profile("user_123", {"name": "John Doe"})

# Multiple fields update
updates = {
    "education_level": "Bachelor's",
    "course_interests": ["IELTS", "German"],
    "learning_goals": ["Study abroad"]
}
memory_manager.update_user_profile("user_123", updates)
```

## 🛠️ Agent Tools

### save_user_memory
```python
# Agent can save important information
save_user_memory(
    content="User prefers morning classes",
    memory_type="preference",
    importance="medium"
)
```

### search_user_memories
```python
# Agent can search for relevant information
search_user_memories(
    query="course preferences",
    memory_types="preference,course_interest",
    limit=5
)
```

### update_user_profile
```python
# Agent can update profile fields
update_user_profile(
    field="course_interests",
    value="Korean Language"
)
```

## 🚀 Production Features

### MongoDB Optimization
- Automatic index creation for performance
- Compound indexes for complex queries
- TTL indexes for cleanup (optional)

### Error Handling
- Graceful fallbacks for missing data
- Comprehensive logging
- Recovery from database issues

### Monitoring
- Memory access statistics
- Profile completeness tracking
- Session activity monitoring

### Scalability
- Tenant-based isolation
- Lazy initialization
- Connection pooling

## 🧪 Testing

Run the comprehensive test suite:
```bash
python test_production_memory.py
```

This tests:
- User profile management
- Structured memory storage
- Memory search functionality
- Permanent session management
- User context generation
- Memory tools integration

## 📈 Benefits

### For Users
- **Consistent Experience**: Same context across all conversations
- **No Repetition**: System remembers previous information
- **Personalized Service**: Tailored responses based on history
- **Progress Tracking**: Learning goals and course completion

### For Developers
- **Type Safety**: Pydantic models prevent data errors
- **Easy Integration**: Simple API for memory operations
- **Flexible Search**: Multiple search strategies
- **Production Ready**: Error handling and monitoring

### For Business
- **Customer Retention**: Better user experience
- **Data Insights**: Rich user behavior data
- **Scalability**: Handles multiple tenants
- **Compliance**: Structured data for auditing

## 🔧 Configuration

### Environment Variables
```bash
GOOGLE_API_KEY=your_gemini_key        # For Gemini
OPENAI_API_KEY=your_openai_key        # For OpenAI
MONGODB_URI=mongodb://localhost:27017  # MongoDB connection
```

### Tenant Setup
```python
# Each tenant gets isolated memory
memory_manager = get_production_memory_manager("tenant_123", "gemini")
```

### LLM Provider
```python
# Supports multiple providers
memory_manager = get_production_memory_manager("tenant_123", "gemini")
memory_manager = get_production_memory_manager("tenant_123", "openai")
```

## 🎯 Use Cases

1. **Educational Platforms**: Course recommendations based on interests
2. **Customer Service**: Long-term customer relationship management
3. **Healthcare**: Patient history and preferences
4. **E-commerce**: Shopping preferences and history
5. **Financial Services**: Client goals and risk preferences
6. **Consulting**: Multi-session project continuity

This production memory system ensures that user information is never lost and conversations maintain context across sessions, providing a truly personalized experience.
