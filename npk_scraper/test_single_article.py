#!/usr/bin/env python3
"""
Test script to verify enhanced metadata extraction on a single article
"""

import requests
from npk_scraper.processors import ArticleProcessor
import json

def test_single_article():
    """Test metadata extraction on a specific article"""
    print("🧪 Testing Enhanced Metadata Extraction")
    print("=" * 50)
    
    # Test article URL
    test_url = "https://nkp.gov.np/full_detail/10301"
    
    print(f"📄 Testing article: {test_url}")
    
    try:
        # Fetch the article
        response = requests.get(test_url)
        response.raise_for_status()
        
        # Initialize processor
        processor = ArticleProcessor()
        
        # Extract text content (simple HTML stripping)
        import re
        text_content = re.sub(r'<[^>]+>', '', response.text)
        
        # Test all extraction methods
        print("\n🔍 Extracting metadata...")
        
        # Test case metadata
        case_metadata = processor.extract_case_metadata(text_content)
        print(f"📋 Case Metadata: {case_metadata}")
        
        # Test dates
        dates = processor.extract_dates(text_content)
        print(f"📅 Dates: {dates}")
        
        # Test parties
        parties = processor.extract_parties_detailed(text_content)
        print(f"👥 Parties:")
        print(f"   Petitioner: {parties.get('petitioner', 'Not found')[:100]}...")
        print(f"   Respondent: {parties.get('respondent', 'Not found')}")
        
        # Test court info
        court_info = processor.extract_court_info(text_content)
        print(f"🏛️ Court Info: {court_info}")
        
        # Test full processing
        article_data = {
            'article_id': '10301',
            'url': test_url,
            'case_content_text': text_content,
            'case_content_html': response.text
        }
        
        processed = processor.process_article(article_data)
        
        print(f"\n✅ Processing completed!")
        print(f"📊 Quality: {processed.get('extraction_quality', 'unknown')}")
        print(f"📏 Content length: {processed.get('content_length', 0)} characters")
        
        # Save test result
        with open('test_article_result.json', 'w', encoding='utf-8') as f:
            json.dump({
                'url': test_url,
                'case_metadata': case_metadata,
                'dates': dates,
                'parties': parties,
                'court_info': court_info,
                'quality': processed.get('extraction_quality'),
                'content_length': processed.get('content_length')
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Test result saved to: test_article_result.json")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ Single article test completed!")
    return True

if __name__ == "__main__":
    test_single_article()
