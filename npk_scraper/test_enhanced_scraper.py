#!/usr/bin/env python3
"""
Test script for the enhanced Nepal Supreme Court scraper
"""

import os
import sys
import json
from pathlib import Path

def test_scraper():
    """Test the enhanced scraper functionality"""
    print("🚀 Testing Enhanced Nepal Supreme Court Scraper")
    print("=" * 50)
    
    # Check if output directory exists
    output_dir = Path("output")
    articles_dir = output_dir / "articles"
    main_output = output_dir / "output.json"
    
    print(f"📁 Output directory: {output_dir}")
    print(f"📁 Articles directory: {articles_dir}")
    print(f"📄 Main output file: {main_output}")
    
    # Check if files exist
    if main_output.exists():
        print("✅ Main output.json found")
        
        # Load and analyze main output
        with open(main_output, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Collection Info:")
        collection_info = data.get('collection_info', {})
        for key, value in collection_info.items():
            print(f"   {key}: {value}")
        
        print(f"\n📈 Data Summary:")
        years_data = data.get('data', {})
        print(f"   Total years found: {len(years_data)}")
        
        for year, year_info in years_data.items():
            months = year_info.get('months', {})
            total_articles = year_info.get('total_articles', 0)
            print(f"   Year {year}: {len(months)} months, {total_articles} articles")
        
        article_summary = data.get('article_summary', {})
        total_articles = article_summary.get('total_articles', 0)
        print(f"   Total articles collected: {total_articles}")
        
    else:
        print("❌ Main output.json not found")
    
    # Check articles directory
    if articles_dir.exists():
        article_files = list(articles_dir.glob("*.json"))
        print(f"\n📚 Individual Articles:")
        print(f"   Found {len(article_files)} article files")
        
        if article_files:
            # Analyze first article file
            first_article = article_files[0]
            print(f"   Sample article: {first_article.name}")
            
            with open(first_article, 'r', encoding='utf-8') as f:
                article_data = json.load(f)
            
            print(f"   Article structure:")
            for key in article_data.keys():
                print(f"     - {key}")
            
            # Show metadata if available
            metadata = article_data.get('metadata', {})
            if metadata:
                print(f"   Metadata sample:")
                for key, value in list(metadata.items())[:5]:
                    print(f"     {key}: {value}")
    else:
        print("❌ Articles directory not found")
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")

if __name__ == "__main__":
    test_scraper()
