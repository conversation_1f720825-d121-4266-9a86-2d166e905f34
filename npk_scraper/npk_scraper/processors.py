"""
Article post-processing utilities for Nepal Supreme Court scraper
Handles text cleaning, metadata extraction, and content structuring
"""

import re
import html
from datetime import datetime
from typing import Dict, List, Optional, Tuple

try:
    from bs4 import BeautifulSoup
except ImportError:
    # Fallback if BeautifulSoup is not available
    BeautifulSoup = None


class ArticleProcessor:
    """Handles post-processing of scraped article content"""
    
    def __init__(self):
        self.nepali_months = {
            '१': '1', '२': '2', '३': '3', '४': '4', '५': '5', '६': '6',
            '७': '7', '८': '8', '९': '9', '१०': '10', '११': '11', '१२': '12'
        }
        
        self.nepali_digits = {
            '०': '0', '१': '1', '२': '2', '३': '3', '४': '4',
            '५': '5', '६': '6', '७': '7', '८': '8', '९': '9'
        }
    
    def clean_html_content(self, html_content: str) -> str:
        """Clean HTML content and extract meaningful text"""
        if not html_content:
            return ""

        if BeautifulSoup is None:
            # Fallback: simple HTML tag removal
            import re
            text = re.sub(r'<[^>]+>', '', html_content)
            text = html.unescape(text)
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            return text

        # Parse HTML with BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        # Get text and clean it
        text = soup.get_text()

        # Clean up whitespace
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)

        return text
    
    def extract_case_number(self, text: str) -> Optional[str]:
        """Extract case number from text"""
        # Common patterns for case numbers in Nepali court documents
        patterns = [
            r'मुद्दा नं[।:]?\s*([०-९\d\-/]+)',
            r'न्यायाधीश[।:]?\s*([०-९\d\-/]+)',
            r'फैसला नं[।:]?\s*([०-९\d\-/]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return self.convert_nepali_digits(match.group(1))
        
        return None
    
    def convert_nepali_digits(self, text: str) -> str:
        """Convert Nepali digits to English digits"""
        if not text:
            return text
        
        result = text
        for nepali, english in self.nepali_digits.items():
            result = result.replace(nepali, english)
        
        return result
    
    def extract_dates(self, text: str) -> Dict[str, Optional[str]]:
        """Extract various dates from the text"""
        dates = {
            'decision_date': None,
            'filing_date': None,
            'hearing_date': None,
            'order_date': None
        }

        # Pattern for Nepali dates
        date_patterns = {
            'decision_date': [
                r'फैसला मिति\s*[:\s]*([०-९\d/\-\.]+)',
                r'निर्णय मिति\s*[:\s]*([०-९\d/\-\.]+)'
            ],
            'filing_date': [
                r'दर्ता मिति\s*[:\s]*([०-९\d/\-\.]+)',
                r'निवेदन मिति\s*[:\s]*([०-९\d/\-\.]+)'
            ],
            'hearing_date': [
                r'सुनुवाई मिति\s*[:\s]*([०-९\d/\-\.]+)',
                r'पेशी मिति\s*[:\s]*([०-९\d/\-\.]+)'
            ],
            'order_date': [
                r'आदेश मिति\s*[:\s]*([०-९\d/\-\.]+)'
            ]
        }

        for date_type, patterns in date_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    dates[date_type] = self.convert_nepali_digits(match.group(1))
                    break

        return dates

    def extract_case_metadata(self, text: str) -> Dict[str, Optional[str]]:
        """Extract case-specific metadata like case number, subject, etc."""
        metadata = {
            'decision_number': None,
            'case_number': None,
            'subject': None,
            'part': None,
            'year': None,
            'month': None,
            'issue_number': None
        }

        # Extract decision number (निर्णय नं.)
        decision_patterns = [
            r'निर्णय नं\.\s*([०-९\d]+)',
            r'निर्णय नं[।:\s]*([०-९\d]+)'
        ]

        for pattern in decision_patterns:
            match = re.search(pattern, text)
            if match:
                metadata['decision_number'] = self.convert_nepali_digits(match.group(1))
                break

        # Extract case number patterns (like ०७८-WF-०१३१)
        case_patterns = [
            r'([०-९\d]{3})-([A-Z]{2})-([०-९\d]{3,4})',
            r'मुद्दा नं[।:\s]*([०-९\d\-/A-Z]+)'
        ]

        for pattern in case_patterns:
            match = re.search(pattern, text)
            if match:
                if len(match.groups()) == 3:
                    # Format like ०७८-WF-०१३१
                    case_num = f"{self.convert_nepali_digits(match.group(1))}-{match.group(2)}-{self.convert_nepali_digits(match.group(3))}"
                    metadata['case_number'] = case_num
                else:
                    metadata['case_number'] = self.convert_nepali_digits(match.group(1))
                break

        # Extract subject/मुद्दा
        subject_patterns = [
            r'मुद्दाः\s*([^\n]+)',
            r'मुद्दा\s*[:\s]*([^\n]+)',
            r'विषयः\s*([^\n]+)',
            r'विषय\s*[:\s]*([^\n]+)'
        ]

        for pattern in subject_patterns:
            match = re.search(pattern, text)
            if match:
                metadata['subject'] = match.group(1).strip()
                break

        # Extract भाग, साल, महिना, अंक (with ** formatting)
        part_match = re.search(r'भाग:\s*\*\*([०-९\d]+)\*\*', text)
        if part_match:
            metadata['part'] = self.convert_nepali_digits(part_match.group(1))

        year_match = re.search(r'साल:\s*\*\*([०-९\d]+)\*\*', text)
        if year_match:
            metadata['year'] = self.convert_nepali_digits(year_match.group(1))

        month_match = re.search(r'महिना:\s*\*\*([^\*]+)\*\*', text)
        if month_match:
            metadata['month'] = month_match.group(1).strip()

        issue_match = re.search(r'अंक:\s*\*\*([०-९\d]+)\*\*', text)
        if issue_match:
            metadata['issue_number'] = self.convert_nepali_digits(issue_match.group(1))

        return metadata
    
    def extract_court_info(self, text: str) -> Dict[str, Optional[str]]:
        """Extract court and judge information"""
        court_info = {
            'court_name': None,
            'bench_type': None,
            'full_court_name': None,
            'judges': []
        }

        # Enhanced court name patterns to capture full court information
        court_patterns = [
            r'(सर्वोच्च अदालत,?\s*पूर्ण इजलास)',
            r'(सर्वोच्च अदालत,?\s*विशेष इजलास)',
            r'(सर्वोच्च अदालत,?\s*संयुक्त इजलास)',
            r'(सर्वोच्च अदालत,?\s*एकल इजलास)',
            r'(सर्वोच्च अदालत)',
            r'(उच्च अदालत[^।\n]*)',
            r'(जिल्ला अदालत[^।\n]*)'
        ]

        for pattern in court_patterns:
            match = re.search(pattern, text)
            if match:
                full_name = match.group(1).strip()
                court_info['full_court_name'] = full_name

                # Extract court name and bench type separately
                if 'सर्वोच्च अदालत' in full_name:
                    court_info['court_name'] = 'सर्वोच्च अदालत'
                    if 'पूर्ण इजलास' in full_name:
                        court_info['bench_type'] = 'पूर्ण इजलास'
                    elif 'विशेष इजलास' in full_name:
                        court_info['bench_type'] = 'विशेष इजलास'
                    elif 'संयुक्त इजलास' in full_name:
                        court_info['bench_type'] = 'संयुक्त इजलास'
                    elif 'एकल इजलास' in full_name:
                        court_info['bench_type'] = 'एकल इजलास'
                elif 'उच्च अदालत' in full_name:
                    court_info['court_name'] = 'उच्च अदालत'
                elif 'जिल्ला अदालत' in full_name:
                    court_info['court_name'] = 'जिल्ला अदालत'
                break

        # Extract judge names - look for patterns with श्री
        judge_patterns = [
            r'न्यायाधीश[।:\s]*श्री\s+([^।\n,]+)',
            r'माननीय न्यायाधीश[।:\s]*श्री\s+([^।\n,]+)',
            r'श्री\s+([^।\n,]+)\s*न्यायाधीश'
        ]

        judges = []
        for pattern in judge_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                judge_name = match.strip()
                if judge_name and judge_name not in judges:
                    judges.append(judge_name)

        court_info['judges'] = judges

        return court_info
    
    def extract_parties_detailed(self, text: str) -> Dict[str, any]:
        """Extract detailed parties information"""
        parties = {
            'petitioner': None,
            'respondent': None,
            'petitioner_advocates': [],
            'respondent_advocates': []
        }

        # Extract निवेदक (Petitioner) - everything between निवेदक : and विरूद्ध
        petitioner_pattern = r'निवेदक\s*:\s*(.*?)(?=विरूद्ध)'
        petitioner_match = re.search(petitioner_pattern, text, re.DOTALL)
        if petitioner_match:
            petitioner_text = petitioner_match.group(1).strip()
            # Clean up extra whitespace and newlines
            petitioner_text = re.sub(r'\s+', ' ', petitioner_text)
            parties['petitioner'] = petitioner_text

        # Extract विपक्षी (Respondent) - everything after विपक्षी : until next major section
        respondent_pattern = r'विपक्षी\s*:\s*([^\n]+?)(?=\n\n|\n[A-Za-z]|$)'
        respondent_match = re.search(respondent_pattern, text)
        if respondent_match:
            respondent_text = respondent_match.group(1).strip()
            # Remove "समेत" if present at the end
            respondent_text = re.sub(r'समेत\s*$', '', respondent_text)
            parties['respondent'] = respondent_text

        # Extract advocates for petitioner
        petitioner_advocate_patterns = [
            r'निवेदकका तर्फबाट\s*:\s*([^विपक्षी]+?)(?=विपक्षी|$)',
            r'निवेदकका तर्फबाट\s*:\s*([^\n]+)'
        ]

        for pattern in petitioner_advocate_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                advocates_text = match.group(1).strip()
                # Extract individual advocate names (look for श्री pattern)
                advocate_names = re.findall(r'श्री\s+([^,\n]+)', advocates_text)
                parties['petitioner_advocates'] = [name.strip() for name in advocate_names]
                break

        # Extract advocates for respondent
        respondent_advocate_patterns = [
            r'विपक्षीका तर्फबाट\s*:\s*([^अवलम्बित]+?)(?=अवलम्बित|$)',
            r'विपक्षीका तर्फबाट\s*:\s*([^\n]+)'
        ]

        for pattern in respondent_advocate_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                advocates_text = match.group(1).strip()
                # Extract individual advocate names (look for श्री pattern)
                advocate_names = re.findall(r'श्री\s+([^,\n]+)', advocates_text)
                parties['respondent_advocates'] = [name.strip() for name in advocate_names]
                break

        return parties
    
    def calculate_content_quality(self, text: str, html: str) -> Dict[str, any]:
        """Calculate quality metrics for the extracted content"""
        quality = {
            'text_length': len(text) if text else 0,
            'html_length': len(html) if html else 0,
            'has_structured_content': False,
            'has_parties': False,
            'has_dates': False,
            'completeness_score': 0.0,
            'quality_rating': 'poor'
        }
        
        if not text:
            return quality
        
        # Check for structured content indicators
        structure_indicators = [
            'फैसला', 'निर्णय', 'आदेश', 'मुद्दा', 'वादी', 'प्रतिवादी'
        ]
        quality['has_structured_content'] = any(indicator in text for indicator in structure_indicators)
        
        # Check for parties
        party_indicators = ['वादी', 'प्रतिवादी', 'पुनरावेदक', 'विपक्षी']
        quality['has_parties'] = any(indicator in text for indicator in party_indicators)
        
        # Check for dates
        date_indicators = ['मिति', 'साल', 'महिना']
        quality['has_dates'] = any(indicator in text for indicator in date_indicators)
        
        # Calculate completeness score
        score = 0
        if quality['text_length'] > 100:
            score += 0.3
        if quality['has_structured_content']:
            score += 0.3
        if quality['has_parties']:
            score += 0.2
        if quality['has_dates']:
            score += 0.2
        
        quality['completeness_score'] = score
        
        # Determine quality rating
        if score >= 0.8:
            quality['quality_rating'] = 'excellent'
        elif score >= 0.6:
            quality['quality_rating'] = 'good'
        elif score >= 0.4:
            quality['quality_rating'] = 'fair'
        else:
            quality['quality_rating'] = 'poor'
        
        return quality
    
    def process_article(self, article_data: Dict) -> Dict:
        """Main processing function for article data"""
        processed = article_data.copy()

        # Extract and clean text content
        html_content = article_data.get('case_content_html', '')
        text_content = article_data.get('case_content_text', '')

        if html_content and not text_content:
            text_content = self.clean_html_content(html_content)
            processed['case_content_text'] = text_content

        # Extract additional metadata
        if text_content:
            # Extract case metadata (case number, subject, part, year, month, issue)
            case_metadata = self.extract_case_metadata(text_content)
            processed['case_metadata'] = case_metadata

            # Extract case number (legacy support)
            if case_metadata.get('case_number'):
                processed['case_number'] = case_metadata['case_number']
            else:
                case_number = self.extract_case_number(text_content)
                if case_number:
                    processed['case_number'] = case_number

            # Extract dates
            dates = self.extract_dates(text_content)
            processed['extracted_dates'] = dates

            # Extract court information
            court_info = self.extract_court_info(text_content)
            processed['court_info'] = court_info

            # Extract detailed parties
            detailed_parties = self.extract_parties_detailed(text_content)
            processed['detailed_parties'] = detailed_parties

            # Calculate quality metrics
            quality = self.calculate_content_quality(text_content, html_content)
            processed['content_quality'] = quality
            processed['extraction_quality'] = quality['quality_rating']

        # Add processing metadata
        processed['processed_at'] = datetime.now().isoformat()
        processed['processor_version'] = '2.0'

        return processed
