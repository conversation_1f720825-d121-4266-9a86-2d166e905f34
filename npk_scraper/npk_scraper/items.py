# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class YearItem(scrapy.Item):
    """Item for year-level data"""
    year = scrapy.Field()
    year_text = scrapy.Field()
    case_count = scrapy.Field()
    url = scrapy.Field()
    months = scrapy.Field()  # List of month data
    scraped_at = scrapy.Field()


class MonthItem(scrapy.Item):
    """Item for month-level data"""
    year = scrapy.Field()
    month = scrapy.Field()
    month_text = scrapy.Field()
    url = scrapy.Field()
    articles = scrapy.Field()  # List of article metadata
    total_articles = scrapy.Field()
    scraped_at = scrapy.Field()


class ArticleMetadataItem(scrapy.Item):
    """Item for article metadata (used in main output.json)"""
    article_id = scrapy.Field()
    title = scrapy.Field()
    url = scrapy.Field()
    year = scrapy.Field()
    month = scrapy.Field()
    decision_date = scrapy.Field()
    views = scrapy.Field()
    parties = scrapy.Field()
    content_length = scrapy.Field()
    file_path = scrapy.Field()  # Path to individual article file
    scraped_at = scrapy.Field()


class ArticleDetailItem(scrapy.Item):
    """Item for detailed article content (used in individual article files)"""
    article_id = scrapy.Field()
    url = scrapy.Field()
    title = scrapy.Field()
    year = scrapy.Field()
    month = scrapy.Field()

    # Decision information
    decision_date = scrapy.Field()
    views = scrapy.Field()
    edition_info = scrapy.Field()  # भाग, साल, महिना, अंक

    # Parties information
    parties = scrapy.Field()
    plaintiff = scrapy.Field()
    defendant = scrapy.Field()

    # Content
    case_content_html = scrapy.Field()
    case_content_text = scrapy.Field()
    content_length = scrapy.Field()

    # Metadata
    scraped_at = scrapy.Field()
    processing_status = scrapy.Field()
    extraction_quality = scrapy.Field()


class HierarchicalOutputItem(scrapy.Item):
    """Main item for hierarchical output structure"""
    collection_info = scrapy.Field()  # Metadata about the collection
    latest_year = scrapy.Field()
    latest_month = scrapy.Field()
    total_articles = scrapy.Field()
    year_data = scrapy.Field()  # Complete year structure with months and articles
    scraped_at = scrapy.Field()
    scraper_version = scrapy.Field()


# Legacy item for backward compatibility
class NkpScraperItem(scrapy.Item):
    url = scrapy.Field()
    title = scrapy.Field()
    content = scrapy.Field()
    links = scrapy.Field()
    timestamp = scrapy.Field()
    case_number = scrapy.Field()
    court_name = scrapy.Field()
    judgment_date = scrapy.Field()
    parties = scrapy.Field()
