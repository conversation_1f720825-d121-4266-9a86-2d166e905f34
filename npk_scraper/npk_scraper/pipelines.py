# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import json
import os
from datetime import datetime
from pathlib import Path
from itemadapter import ItemAdapter
from .items import (
    YearItem, MonthItem, ArticleMetadataItem,
    ArticleDetailItem, HierarchicalOutputItem
)
from .processors import ArticleProcessor


class HierarchicalOutputPipeline:
    """Pipeline for handling hierarchical output structure"""

    def __init__(self):
        self.output_dir = Path("output")
        self.articles_dir = self.output_dir / "articles"
        self.main_output_file = self.output_dir / "output.json"

        # Initialize article processor
        self.article_processor = ArticleProcessor()

        # Data storage for building hierarchical structure
        self.year_data = {}
        self.article_metadata = []
        self.collection_info = {
            "scraper_version": "2.0",
            "collection_type": "latest_year_month",
            "started_at": datetime.now().isoformat()
        }

    def open_spider(self, spider):
        """Initialize output directories"""
        self.output_dir.mkdir(exist_ok=True)
        self.articles_dir.mkdir(exist_ok=True)
        spider.logger.info(f"Created output directories: {self.output_dir}, {self.articles_dir}")

    def close_spider(self, spider):
        """Generate final hierarchical output.json"""
        if self.year_data or self.article_metadata:
            # Build the final hierarchical structure
            hierarchical_data = {
                "collection_info": {
                    **self.collection_info,
                    "completed_at": datetime.now().isoformat(),
                    "total_articles_collected": len(self.article_metadata)
                },
                "data": self.year_data,
                "article_summary": {
                    "total_articles": len(self.article_metadata),
                    "articles": self.article_metadata
                }
            }

            # Write main output.json
            with open(self.main_output_file, 'w', encoding='utf-8') as f:
                json.dump(hierarchical_data, f, ensure_ascii=False, indent=2)

            spider.logger.info(f"Generated main output file: {self.main_output_file}")
            spider.logger.info(f"Total articles processed: {len(self.article_metadata)}")

    def process_item(self, item, spider):
        adapter = ItemAdapter(item)

        if isinstance(item, ArticleDetailItem):
            return self._process_article_detail(adapter, spider)
        elif isinstance(item, ArticleMetadataItem):
            return self._process_article_metadata(adapter, spider)
        elif isinstance(item, MonthItem):
            return self._process_month_data(adapter, spider)
        elif isinstance(item, YearItem):
            return self._process_year_data(adapter, spider)
        else:
            # Handle legacy items or pass through
            return item

    def _process_article_detail(self, adapter, spider):
        """Process detailed article content and save to individual file"""
        article_id = adapter.get('article_id')

        if not article_id:
            spider.logger.warning("Article missing ID, skipping individual file creation")
            return adapter.item

        # Create individual article file
        article_file = self.articles_dir / f"{article_id}.json"

        # Prepare raw article data for processing
        raw_article_data = {
            "article_id": article_id,
            "url": adapter.get('url'),
            "title": adapter.get('title'),
            "year": adapter.get('year'),
            "month": adapter.get('month'),
            "decision_date": adapter.get('decision_date'),
            "views": adapter.get('views'),
            "edition_info": adapter.get('edition_info', {}),
            "parties": adapter.get('parties', {}),
            "case_content_html": adapter.get('case_content_html'),
            "case_content_text": adapter.get('case_content_text'),
            "content_length": adapter.get('content_length', 0),
            "scraped_at": adapter.get('scraped_at'),
            "processing_status": adapter.get('processing_status', 'completed'),
            "extraction_quality": adapter.get('extraction_quality', 'good')
        }

        # Process article with enhanced extraction
        processed_article = self.article_processor.process_article(raw_article_data)

        # Structure the final article data
        article_data = {
            "article_id": article_id,
            "url": processed_article.get('url'),
            "title": processed_article.get('title'),
            "metadata": {
                "year": processed_article.get('year'),
                "month": processed_article.get('month'),
                "decision_date": processed_article.get('decision_date'),
                "views": processed_article.get('views'),
                "edition_info": processed_article.get('edition_info', {}),
                "content_length": processed_article.get('content_length', 0),
                "scraped_at": processed_article.get('scraped_at'),
                "processed_at": processed_article.get('processed_at'),
                "processing_status": processed_article.get('processing_status', 'completed'),
                "extraction_quality": processed_article.get('extraction_quality', 'good'),
                "content_quality": processed_article.get('content_quality', {}),
                "case_number": processed_article.get('case_number'),
                "case_metadata": processed_article.get('case_metadata', {})
            },
            "parties": {
                "basic_parties": processed_article.get('parties', {}),
                "detailed_parties": processed_article.get('detailed_parties', {}),
                "plaintiff": processed_article.get('plaintiff'),
                "defendant": processed_article.get('defendant')
            },
            "dates": processed_article.get('extracted_dates', {}),
            "court_info": processed_article.get('court_info', {}),
            "content": {
                "html": processed_article.get('case_content_html'),
                "text": processed_article.get('case_content_text')
            }
        }

        # Write individual article file
        with open(article_file, 'w', encoding='utf-8') as f:
            json.dump(article_data, f, ensure_ascii=False, indent=2)

        spider.logger.info(f"Saved article {article_id} to {article_file}")

        # Add to article metadata for main output
        metadata = {
            "article_id": article_id,
            "title": adapter.get('title'),
            "url": adapter.get('url'),
            "year": adapter.get('year'),
            "month": adapter.get('month'),
            "decision_date": adapter.get('decision_date'),
            "views": adapter.get('views'),
            "parties": adapter.get('parties', {}),
            "content_length": adapter.get('content_length', 0),
            "file_path": f"articles/{article_id}.json",
            "scraped_at": adapter.get('scraped_at')
        }
        self.article_metadata.append(metadata)

        return adapter.item

    def _process_article_metadata(self, adapter, spider):
        """Process article metadata for main output structure"""
        metadata = {
            "article_id": adapter.get('article_id'),
            "title": adapter.get('title'),
            "url": adapter.get('url'),
            "year": adapter.get('year'),
            "month": adapter.get('month'),
            "decision_date": adapter.get('decision_date'),
            "views": adapter.get('views'),
            "parties": adapter.get('parties', {}),
            "content_length": adapter.get('content_length', 0),
            "file_path": adapter.get('file_path'),
            "scraped_at": adapter.get('scraped_at')
        }
        self.article_metadata.append(metadata)
        return adapter.item

    def _process_month_data(self, adapter, spider):
        """Process month-level data for hierarchical structure"""
        year = adapter.get('year')
        month = adapter.get('month')

        if year not in self.year_data:
            self.year_data[year] = {
                "year": year,
                "months": {},
                "total_articles": 0
            }

        month_data = {
            "month": month,
            "month_text": adapter.get('month_text'),
            "url": adapter.get('url'),
            "total_articles": adapter.get('total_articles', 0),
            "articles": adapter.get('articles', []),
            "scraped_at": adapter.get('scraped_at')
        }

        self.year_data[year]["months"][month] = month_data
        self.year_data[year]["total_articles"] += month_data["total_articles"]

        spider.logger.info(f"Processed month data: {year}/{month} with {month_data['total_articles']} articles")
        return adapter.item

    def _process_year_data(self, adapter, spider):
        """Process year-level data for hierarchical structure"""
        year = adapter.get('year')

        if year not in self.year_data:
            self.year_data[year] = {
                "year": year,
                "months": {},
                "total_articles": 0
            }

        # Update year-level information
        self.year_data[year].update({
            "year_text": adapter.get('year_text'),
            "case_count": adapter.get('case_count'),
            "url": adapter.get('url'),
            "scraped_at": adapter.get('scraped_at')
        })

        spider.logger.info(f"Processed year data: {year}")
        return adapter.item


class NpkScraperPipeline:
    """Legacy pipeline for backward compatibility"""
    def process_item(self, item, spider):
        return item
