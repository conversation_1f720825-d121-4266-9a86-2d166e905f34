import scrapy
import re
from urllib.parse import urljoin, parse_qs, urlparse
from datetime import datetime


class NkpTestSpider(scrapy.Spider):
    name = 'nkp_test'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
        'FEEDS': {
            'nkp_test_output.json': {
                'format': 'json',
                'encoding': 'utf8',
                'store_empty': False,
                'indent': 2,
            },
        },
    }

    def start_requests(self):
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0, i'
        }
        
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                headers=headers,
                callback=self.parse_browse_page
            )

    def parse_browse_page(self, response):
        """Parse the main browse page and extract year information"""
        self.logger.info(f"Processing browse page: {response.url}")
        
        # Extract year links
        year_links = response.css('a[href*="browse_monthly"]')
        
        years_data = []
        for link in year_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            
            if href and text:
                full_url = urljoin(response.url, href)
                
                # Extract year from URL
                parsed_url = urlparse(full_url)
                query_params = parse_qs(parsed_url.query)
                year = query_params.get('year', ['unknown'])[0]
                
                # Extract case count from text (e.g., "बि.स. २०८० (२२१ थान)")
                case_count = None
                if '(' in text and ')' in text:
                    count_match = re.search(r'\((\d+)\s*थान\)', text)
                    if count_match:
                        case_count = int(count_match.group(1))
                
                years_data.append({
                    'year': year,
                    'year_text': text.strip(),
                    'case_count': case_count,
                    'url': full_url
                })
        
        # Extract recent cases from the same page
        recent_cases = []
        recent_case_links = response.css('.post-title a[href*="full_detail"]')
        
        for case_link in recent_case_links:
            case_href = case_link.css('::attr(href)').get()
            case_title = case_link.css('::text').get()
            
            if case_href and case_title:
                case_url = urljoin(response.url, case_href)
                case_id = case_href.split('/')[-1] if '/' in case_href else 'unknown'
                
                recent_cases.append({
                    'case_id': case_id,
                    'title': case_title.strip(),
                    'url': case_url
                })
        
        # Return the extracted data
        yield {
            'page_type': 'browse_main',
            'url': response.url,
            'scraped_at': datetime.now().isoformat(),
            'total_years_found': len(years_data),
            'total_recent_cases_found': len(recent_cases),
            'years': years_data,
            'recent_cases': recent_cases,
            'page_title': response.css('title::text').get(),
            'status': 'success'
        }
        
        self.logger.info(f"Successfully extracted {len(years_data)} years and {len(recent_cases)} recent cases")
