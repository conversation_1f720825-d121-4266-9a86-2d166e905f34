import scrapy
import re
from urllib.parse import urljoin, parse_qs, urlparse
from datetime import datetime


class NkpComprehensiveScraper<PERSON>pider(scrapy.Spider):
    name = 'nkp_comprehensive'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
        'FEEDS': {
            'nkp_cases_%(time)s.json': {
                'format': 'json',
                'encoding': 'utf8',
                'store_empty': False,
                'indent': 2,
            },
        },
    }

    def start_requests(self):
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0, i'
        }
        
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                headers=headers,
                callback=self.parse
            )

    def parse(self, response):
        """Extract all year URLs from browse page"""
        year_links = response.css('a[href*="browse_monthly"]::attr(href)').getall()
        
        self.logger.info(f"Found {len(year_links)} year links")
        
        for link in year_links:
            full_url = urljoin(response.url, link)
            
            # Extract year from URL for metadata
            parsed_url = urlparse(full_url)
            query_params = parse_qs(parsed_url.query)
            year = query_params.get('year', ['unknown'])[0]
            
            self.logger.info(f"Processing year: {year}")
            
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_months,
                meta={'year': year},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_months(self, response):
        """Extract all month URLs from year pages"""
        year = response.meta.get('year', 'unknown')
        month_links = response.css('a[href*="advance_search"]::attr(href)').getall()
        
        self.logger.info(f"Found {len(month_links)} month links for year {year}")
        
        for link in month_links:
            full_url = urljoin(response.url, link)
            
            # Extract month from URL for metadata
            parsed_url = urlparse(full_url)
            query_params = parse_qs(parsed_url.query)
            month = query_params.get('month', ['unknown'])[0]
            
            self.logger.info(f"Processing year {year}, month {month}")
            
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_cases,
                meta={'year': year, 'month': month},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_cases(self, response):
        """Extract individual case URLs from month pages and follow them"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        
        # Extract case detail links
        case_links = response.css('a[href*="full_detail"]::attr(href)').getall()
        
        self.logger.info(f"Found {len(case_links)} cases for year {year}, month {month}")
        
        for link in case_links:
            full_url = urljoin(response.url, link)
            
            # Extract case ID from URL
            case_id = link.split('/')[-1] if '/' in link else 'unknown'
            
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_case_detail,
                meta={'year': year, 'month': month, 'case_id': case_id},
                headers=response.request.headers,
                dont_filter=True
            )
        
        # Handle pagination if exists
        next_page = response.css('a[href*="page="]::attr(href)').get()
        if next_page:
            next_url = urljoin(response.url, next_page)
            self.logger.info(f"Following pagination: {next_url}")
            yield scrapy.Request(
                url=next_url,
                callback=self.parse_cases,
                meta={'year': year, 'month': month},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_case_detail(self, response):
        """Extract detailed case information from individual case pages"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        case_id = response.meta.get('case_id', 'unknown')
        
        # Extract case title
        title = response.css('h1.post-title a::text').get()
        if not title:
            title = response.css('h1.post-title::text').get()
        
        # Extract decision summary information
        decision_info = {}
        
        # Extract edition info (भाग, साल, महिना, अंक)
        edition_info = response.css('#edition-info span')
        for info in edition_info:
            text = info.css('::text').getall()
            if text:
                full_text = ''.join(text).strip()
                if ':' in full_text:
                    key, value = full_text.split(':', 1)
                    decision_info[key.strip()] = value.strip()
        
        # Extract decision date and views
        post_meta = response.css('.post-meta::text').getall()
        decision_date = None
        views = None
        
        for meta_text in post_meta:
            if 'फैसला मिति' in meta_text:
                decision_date = meta_text.split(':')[-1].strip() if ':' in meta_text else None
            elif meta_text.strip().isdigit():
                views = meta_text.strip()
        
        # Extract the main case content
        case_content = response.css('#faisala_detail').get()
        if not case_content:
            case_content = response.css('.para-sections').get()
        
        # Extract clean text content
        case_text = response.css('#faisala_detail ::text').getall()
        if not case_text:
            case_text = response.css('.para-sections ::text').getall()
        
        clean_text = ' '.join([text.strip() for text in case_text if text.strip()])
        
        # Extract parties information from the listing page format if available
        parties_info = self.extract_parties_info(response)
        
        yield {
            'url': response.url,
            'case_id': case_id,
            'year': year,
            'month': month,
            'title': title.strip() if title else None,
            'decision_date': decision_date,
            'views': views,
            'edition_info': decision_info,
            'parties': parties_info,
            'case_content_html': case_content,
            'case_content_text': clean_text,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(clean_text) if clean_text else 0
        }

    def extract_parties_info(self, response):
        """Extract parties information (plaintiff/defendant)"""
        parties = {}
        
        # Look for parties information in various possible locations
        parties_text = response.css('.type-details span::text').getall()
        
        for text in parties_text:
            if 'पुनरावेदक' in text or 'वादी' in text:
                parties['plaintiff'] = text.strip()
            elif 'प्रतिवादी' in text or 'विपक्षी' in text:
                parties['defendant'] = text.strip()
        
        return parties
