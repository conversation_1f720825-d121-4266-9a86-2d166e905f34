import scrapy
import re
from urllib.parse import urljoin, parse_qs, urlparse
from datetime import datetime
from ..items import (
    YearItem, MonthItem, ArticleMetadataItem, 
    ArticleDetailItem, HierarchicalOutputItem
)


class NkpHierarchicalSpider(scrapy.Spider):
    name = 'nkp_hierarchical'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
        'ITEM_PIPELINES': {
            'npk_scraper.pipelines.HierarchicalOutputPipeline': 300,
        },
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.latest_year = None
        self.latest_month = None
        self.year_data = {}
        self.collected_articles = []

    def start_requests(self):
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0, i'
        }
        
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                headers=headers,
                callback=self.parse_browse_page
            )

    def parse_browse_page(self, response):
        """Parse the main browse page and identify the latest year"""
        self.logger.info(f"Processing browse page: {response.url}")
        
        # Extract year links
        year_links = response.css('a[href*="browse_monthly"]')
        
        years_data = []
        latest_year_num = 0
        latest_year_link = None
        
        for link in year_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            
            if href and text:
                full_url = urljoin(response.url, href)
                
                # Extract year from URL
                parsed_url = urlparse(full_url)
                query_params = parse_qs(parsed_url.query)
                year = query_params.get('year', ['unknown'])[0]
                
                # Extract case count from text (e.g., "बि.स. २०८० (२२१ थान)")
                case_count = None
                if '(' in text and ')' in text:
                    count_match = re.search(r'\((\d+)\s*थान\)', text)
                    if count_match:
                        case_count = int(count_match.group(1))
                
                # Try to determine the latest year (assuming higher numbers are more recent)
                try:
                    year_num = int(year)
                    if year_num > latest_year_num:
                        latest_year_num = year_num
                        latest_year_link = full_url
                        self.latest_year = year
                except ValueError:
                    pass
                
                year_data = {
                    'year': year,
                    'year_text': text.strip(),
                    'case_count': case_count,
                    'url': full_url
                }
                years_data.append(year_data)
                
                # Yield year item for pipeline processing
                yield YearItem(**year_data, scraped_at=datetime.now().isoformat())
        
        self.logger.info(f"Found {len(years_data)} years. Latest year identified: {self.latest_year}")
        
        # Process the latest year to find the latest month
        if latest_year_link:
            yield scrapy.Request(
                url=latest_year_link,
                callback=self.parse_latest_year_months,
                meta={'year': self.latest_year},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_latest_year_months(self, response):
        """Parse the latest year page and identify the latest month"""
        year = response.meta.get('year', 'unknown')
        self.logger.info(f"Processing latest year page: {year}")
        
        month_links = response.css('a[href*="advance_search"]')
        
        months_data = []
        latest_month_num = 0
        latest_month_link = None
        
        for link in month_links:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            
            if href and text:
                full_url = urljoin(response.url, href)
                
                # Extract month from URL
                parsed_url = urlparse(full_url)
                query_params = parse_qs(parsed_url.query)
                month = query_params.get('month', ['unknown'])[0]
                
                # Try to determine the latest month (assuming higher numbers are more recent)
                try:
                    month_num = int(month)
                    if month_num > latest_month_num:
                        latest_month_num = month_num
                        latest_month_link = full_url
                        self.latest_month = month
                except ValueError:
                    pass
                
                month_data = {
                    'year': year,
                    'month': month,
                    'month_text': text.strip(),
                    'url': full_url
                }
                months_data.append(month_data)
        
        self.logger.info(f"Found {len(months_data)} months in year {year}. Latest month identified: {self.latest_month}")
        
        # Process only the latest month
        if latest_month_link:
            self.logger.info(f"Collecting data for latest period: {year}/{self.latest_month}")
            yield scrapy.Request(
                url=latest_month_link,
                callback=self.parse_latest_month_articles,
                meta={'year': year, 'month': self.latest_month},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_latest_month_articles(self, response):
        """Parse the latest month page and collect all articles"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        
        self.logger.info(f"Processing articles for {year}/{month}")
        
        # Extract case detail links
        case_links = response.css('a[href*="full_detail"]::attr(href)').getall()
        
        self.logger.info(f"Found {len(case_links)} articles for {year}/{month}")
        
        articles_metadata = []
        
        for link in case_links:
            full_url = urljoin(response.url, link)
            
            # Extract case ID from URL
            article_id = link.split('/')[-1] if '/' in link else 'unknown'
            
            # Create article metadata
            article_meta = {
                'article_id': article_id,
                'url': full_url,
                'year': year,
                'month': month,
                'file_path': f'articles/{article_id}.json'
            }
            articles_metadata.append(article_meta)
            
            # Request detailed article content
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_article_detail,
                meta={'year': year, 'month': month, 'article_id': article_id},
                headers=response.request.headers,
                dont_filter=True
            )
        
        # Yield month item with article metadata
        month_item = MonthItem(
            year=year,
            month=month,
            url=response.url,
            articles=articles_metadata,
            total_articles=len(articles_metadata),
            scraped_at=datetime.now().isoformat()
        )
        yield month_item
        
        # Handle pagination if exists
        next_page = response.css('a[href*="page="]::attr(href)').get()
        if next_page:
            next_url = urljoin(response.url, next_page)
            self.logger.info(f"Following pagination: {next_url}")
            yield scrapy.Request(
                url=next_url,
                callback=self.parse_latest_month_articles,
                meta={'year': year, 'month': month},
                headers=response.request.headers,
                dont_filter=True
            )

    def parse_article_detail(self, response):
        """Extract detailed article information"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        article_id = response.meta.get('article_id', 'unknown')
        
        # Extract article title
        title = response.css('h1.post-title a::text').get()
        if not title:
            title = response.css('h1.post-title::text').get()
        
        # Extract decision summary information
        decision_info = {}
        
        # Extract edition info (भाग, साल, महिना, अंक)
        edition_info = response.css('#edition-info span')
        for info in edition_info:
            text = info.css('::text').getall()
            if text:
                full_text = ''.join(text).strip()
                if ':' in full_text:
                    key, value = full_text.split(':', 1)
                    decision_info[key.strip()] = value.strip()
        
        # Extract decision date and views
        post_meta = response.css('.post-meta::text').getall()
        decision_date = None
        views = None
        
        for meta_text in post_meta:
            if 'फैसला मिति' in meta_text:
                decision_date = meta_text.split(':')[-1].strip() if ':' in meta_text else None
            elif meta_text.strip().isdigit():
                views = meta_text.strip()
        
        # Extract the main case content
        case_content = response.css('#faisala_detail').get()
        if not case_content:
            case_content = response.css('.para-sections').get()
        
        # Extract clean text content
        case_text = response.css('#faisala_detail ::text').getall()
        if not case_text:
            case_text = response.css('.para-sections ::text').getall()
        
        clean_text = ' '.join([text.strip() for text in case_text if text.strip()])
        
        # Extract parties information
        parties_info = self.extract_parties_info(response)
        
        # Create detailed article item
        article_detail = ArticleDetailItem(
            article_id=article_id,
            url=response.url,
            title=title.strip() if title else None,
            year=year,
            month=month,
            decision_date=decision_date,
            views=views,
            edition_info=decision_info,
            parties=parties_info,
            plaintiff=parties_info.get('plaintiff'),
            defendant=parties_info.get('defendant'),
            case_content_html=case_content,
            case_content_text=clean_text,
            content_length=len(clean_text) if clean_text else 0,
            scraped_at=datetime.now().isoformat(),
            processing_status='completed',
            extraction_quality='good' if clean_text and len(clean_text) > 100 else 'poor'
        )
        
        yield article_detail
        
        self.logger.info(f"Processed article {article_id}: {title[:50] if title else 'No title'}...")

    def extract_parties_info(self, response):
        """Extract parties information (plaintiff/defendant)"""
        parties = {}
        
        # Look for parties information in various possible locations
        parties_text = response.css('.type-details span::text').getall()
        
        for text in parties_text:
            if 'पुनरावेदक' in text or 'वादी' in text:
                parties['plaintiff'] = text.strip()
            elif 'प्रतिवादी' in text or 'विपक्षी' in text:
                parties['defendant'] = text.strip()
        
        return parties
