import scrapy
import re
from urllib.parse import urljoin, parse_qs, urlparse
from datetime import datetime


class NkpComprehensiveSpider(scrapy.Spider):
    name = 'nkp_comprehensive'
    allowed_domains = ['nkp.gov.np']
    start_urls = ['https://nkp.gov.np/browse']
    
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0',
        'FEEDS': {
            'nkp_comprehensive_output.json': {
                'format': 'json',
                'encoding': 'utf8',
                'store_empty': False,
                'indent': 2,
            },
        },
    }

    def start_requests(self):
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=0, i'
        }
        
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                headers=headers,
                callback=self.parse_browse_page
            )

    def parse_browse_page(self, response):
        """Parse the main browse page and extract year URLs - process only first 2 years for testing"""
        self.logger.info(f"Processing browse page: {response.url}")
        
        # Extract year links
        year_links = response.css('a[href*="browse_monthly"]')
        
        # For testing, only process first 2 years
        test_years = year_links[:2]
        
        for link in test_years:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            
            if href and text:
                full_url = urljoin(response.url, href)
                
                # Extract year from URL
                parsed_url = urlparse(full_url)
                query_params = parse_qs(parsed_url.query)
                year = query_params.get('year', ['unknown'])[0]
                
                # Extract case count from text
                case_count = None
                if '(' in text and ')' in text:
                    count_match = re.search(r'\((\d+)\s*थान\)', text)
                    if count_match:
                        case_count = int(count_match.group(1))
                
                self.logger.info(f"Following year {year} with {case_count} cases: {full_url}")
                
                yield scrapy.Request(
                    url=full_url,
                    callback=self.parse_year_page,
                    meta={
                        'year': year,
                        'year_text': text.strip(),
                        'year_case_count': case_count
                    },
                    headers=response.request.headers,
                    dont_filter=True
                )

    def parse_year_page(self, response):
        """Parse individual year page and extract monthly URLs - process only first 2 months for testing"""
        year = response.meta.get('year', 'unknown')
        year_text = response.meta.get('year_text', '')
        year_case_count = response.meta.get('year_case_count', 0)
        
        self.logger.info(f"Processing year page for {year}: {response.url}")
        
        # Extract month links
        month_links = response.css('a[href*="advance_search"]')
        
        # For testing, only process first 2 months
        test_months = month_links[:2]
        
        for link in test_months:
            href = link.css('::attr(href)').get()
            text = link.css('::text').get()
            
            if href and text:
                full_url = urljoin(response.url, href)
                
                # Extract month from URL
                parsed_url = urlparse(full_url)
                query_params = parse_qs(parsed_url.query)
                month = query_params.get('month', ['unknown'])[0]
                
                # Extract month name and case count from text
                month_name = None
                month_case_count = None
                
                if text.strip():
                    # Extract month name (before parentheses)
                    if '(' in text:
                        month_name = text.split('(')[0].strip()
                        # Extract case count
                        count_match = re.search(r'\((\d+)\s*थान\)', text)
                        if count_match:
                            month_case_count = int(count_match.group(1))
                    else:
                        month_name = text.strip()
                
                self.logger.info(f"Following year {year}, month {month} ({month_name}) with {month_case_count} cases")
                
                yield scrapy.Request(
                    url=full_url,
                    callback=self.parse_month_page,
                    meta={
                        'year': year,
                        'year_text': year_text,
                        'year_case_count': year_case_count,
                        'month': month,
                        'month_name': month_name,
                        'month_case_count': month_case_count,
                        'page_number': 1
                    },
                    headers=response.request.headers,
                    dont_filter=True
                )

    def parse_month_page(self, response):
        """Parse month page and extract case URLs, handle pagination"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        month_name = response.meta.get('month_name', 'unknown')
        page_number = response.meta.get('page_number', 1)
        
        self.logger.info(f"Processing month page for year {year}, month {month} ({month_name}), page {page_number}")
        
        # Extract case detail links
        case_links = response.css('a[href*="full_detail"]')
        
        cases_found = 0
        for link in case_links:
            href = link.css('::attr(href)').get()
            
            if href and 'full_detail' in href:
                full_url = urljoin(response.url, href)
                
                # Extract case ID from URL
                case_id = href.split('/')[-1] if '/' in href else 'unknown'
                
                # Extract case title
                case_title = link.css('::text').get()
                if not case_title:
                    # Try to get title from parent elements
                    case_title = link.xpath('./ancestor::h3//text()').get()
                
                cases_found += 1
                
                yield scrapy.Request(
                    url=full_url,
                    callback=self.parse_case_detail,
                    meta={
                        'year': year,
                        'month': month,
                        'month_name': month_name,
                        'case_id': case_id,
                        'case_title': case_title.strip() if case_title else None,
                        'page_number': page_number
                    },
                    headers=response.request.headers,
                    dont_filter=True
                )
        
        self.logger.info(f"Found {cases_found} cases on page {page_number}")
        
        # Handle pagination - look for next page link
        # Check for pagination links like [2], [»], etc.
        next_page_links = response.css('a[href*="per_page=20"]')
        
        # For testing, only follow first pagination page
        if page_number == 1 and next_page_links:
            next_link = next_page_links[0]  # Take first pagination link
            next_href = next_link.css('::attr(href)').get()
            
            if next_href:
                next_url = urljoin(response.url, next_href)
                self.logger.info(f"Following pagination to page 2: {next_url}")
                
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_month_page,
                    meta={
                        'year': year,
                        'month': month,
                        'month_name': month_name,
                        'month_case_count': response.meta.get('month_case_count'),
                        'page_number': 2
                    },
                    headers=response.request.headers,
                    dont_filter=True
                )

    def parse_case_detail(self, response):
        """Extract detailed case information from individual case pages"""
        year = response.meta.get('year', 'unknown')
        month = response.meta.get('month', 'unknown')
        month_name = response.meta.get('month_name', 'unknown')
        case_id = response.meta.get('case_id', 'unknown')
        case_title = response.meta.get('case_title')
        page_number = response.meta.get('page_number', 1)
        
        self.logger.info(f"Processing case detail: {case_id}")
        
        # Extract case title from detail page if not available
        if not case_title:
            case_title = response.css('h1.post-title a::text').get()
            if not case_title:
                case_title = response.css('h1.post-title::text').get()
        
        # Extract edition info (भाग, साल, महिना, अंक)
        edition_info = {}
        edition_spans = response.css('#edition-info span')
        for span in edition_spans:
            text = ''.join(span.css('::text').getall()).strip()
            if ':' in text:
                key, value = text.split(':', 1)
                edition_info[key.strip()] = value.strip()
        
        # Extract decision date and views from post-meta
        decision_date = None
        views = None
        
        post_meta_text = response.css('.post-meta::text').getall()
        for text in post_meta_text:
            if 'फैसला मिति' in text and ':' in text:
                decision_date = text.split(':')[-1].strip()
            elif text.strip().isdigit():
                views = int(text.strip())
        
        # Extract the main case content
        case_content_html = response.css('#faisala_detail').get()
        if not case_content_html:
            case_content_html = response.css('.para-sections').get()
        
        # Extract clean text content
        case_text_parts = response.css('#faisala_detail ::text').getall()
        if not case_text_parts:
            case_text_parts = response.css('.para-sections ::text').getall()
        
        clean_text = ' '.join([text.strip() for text in case_text_parts if text.strip()])
        
        # Extract parties information
        parties_info = self.extract_parties_info(response)
        
        yield {
            'url': response.url,
            'case_id': case_id,
            'year': year,
            'month': month,
            'month_name': month_name,
            'page_number': page_number,
            'title': case_title.strip() if case_title else None,
            'decision_date': decision_date,
            'views': views,
            'edition_info': edition_info,
            'parties': parties_info,
            'case_content_html': case_content_html,
            'case_content_text': clean_text,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(clean_text) if clean_text else 0
        }

    def extract_parties_info(self, response):
        """Extract parties information (plaintiff/defendant)"""
        parties = {}
        
        # Look for parties information in various possible locations
        parties_elements = response.css('.type-details span')
        
        for element in parties_elements:
            text = ''.join(element.css('::text').getall()).strip()
            
            if 'पुनरावेदक' in text or 'प्रतिवादी' in text:
                parties['defendant'] = text
            elif 'विपक्षी' in text or 'वादी' in text:
                parties['plaintiff'] = text
        
        return parties
