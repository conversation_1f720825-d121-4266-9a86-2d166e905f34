/**
 * Booking Service
 * Handles booking API communication and management
 */

import { http } from './baseHttp';

// Types
export interface BookingCreateRequest {
  user_name: string;
  user_email: string;
  user_phone: string;
  course_name: string;
  course_code: string;
  time_slot: string;
}

export interface BookingResponse {
  booking_id: string;
  user_name: string;
  course_name: string;
  course_code: string;
  time_slot: string;
  booking_date: Date;
  status: string;
}

export interface BookingUpdateRequest {
  status: string; // confirmed, cancelled, pending
}

export interface BookingStatsResponse {
  total_bookings: number;
  status_breakdown: Record<string, number>;
  recent_bookings_7_days: number;
}

export interface BookingActionResponse {
  success: boolean;
  message: string;
  booking_id: string;
}

/**
 * Booking Service Class
 */
class BookingService {
  /**
   * Create a new booking
   */
  async createBooking(bookingData: BookingCreateRequest): Promise<BookingResponse> {
    try {
      const response = await http.post<BookingResponse>('/api/v1/bookings/', bookingData);
      return {
        ...response.data,
        booking_date: new Date(response.data.booking_date)
      };
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to create booking');
    }
  }

  /**
   * Get all bookings for current user
   */
  async getUserBookings(limit: number = 50): Promise<BookingResponse[]> {
    try {
      const response = await http.get<BookingResponse[]>(`/api/v1/bookings/?limit=${limit}`);
      return response.data.map(booking => ({
        ...booking,
        booking_date: new Date(booking.booking_date)
      }));
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get bookings');
    }
  }

  /**
   * Get a specific booking by ID
   */
  async getBooking(bookingId: string): Promise<BookingResponse> {
    try {
      const response = await http.get<BookingResponse>(`/api/v1/bookings/${bookingId}`);
      return {
        ...response.data,
        booking_date: new Date(response.data.booking_date)
      };
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get booking');
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(bookingId: string, status: string): Promise<BookingActionResponse> {
    try {
      const response = await http.put<BookingActionResponse>(`/api/v1/bookings/${bookingId}`, {
        status
      });
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to update booking');
    }
  }

  /**
   * Cancel a booking
   */
  async cancelBooking(bookingId: string): Promise<BookingActionResponse> {
    try {
      const response = await http.delete<BookingActionResponse>(`/api/v1/bookings/${bookingId}`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to cancel booking');
    }
  }

  /**
   * Permanently delete a booking
   */
  async deleteBookingPermanent(bookingId: string): Promise<BookingActionResponse> {
    try {
      const response = await http.delete<BookingActionResponse>(`/api/v1/bookings/${bookingId}/permanent`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to delete booking');
    }
  }

  /**
   * Get bookings by status
   */
  async getBookingsByStatus(status: string, limit: number = 100): Promise<BookingResponse[]> {
    try {
      const response = await http.get<BookingResponse[]>(`/api/v1/bookings/status/${status}?limit=${limit}`);
      return response.data.map(booking => ({
        ...booking,
        booking_date: new Date(booking.booking_date)
      }));
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get bookings by status');
    }
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(): Promise<BookingStatsResponse> {
    try {
      const response = await http.get<BookingStatsResponse>('/api/v1/bookings/stats/overview');
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get booking stats');
    }
  }

  /**
   * Confirm a booking
   */
  async confirmBooking(bookingId: string): Promise<BookingActionResponse> {
    return this.updateBookingStatus(bookingId, 'confirmed');
  }

  /**
   * Set booking to pending
   */
  async setPendingBooking(bookingId: string): Promise<BookingActionResponse> {
    return this.updateBookingStatus(bookingId, 'pending');
  }

  /**
   * Get confirmed bookings
   */
  async getConfirmedBookings(limit: number = 100): Promise<BookingResponse[]> {
    return this.getBookingsByStatus('confirmed', limit);
  }

  /**
   * Get cancelled bookings
   */
  async getCancelledBookings(limit: number = 100): Promise<BookingResponse[]> {
    return this.getBookingsByStatus('cancelled', limit);
  }

  /**
   * Get pending bookings
   */
  async getPendingBookings(limit: number = 100): Promise<BookingResponse[]> {
    return this.getBookingsByStatus('pending', limit);
  }
}

// Export singleton instance
export const bookingService = new BookingService();
export default bookingService;
