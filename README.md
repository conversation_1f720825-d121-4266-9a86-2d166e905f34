# Multi-Tenant Customer Service API

A FastAPI-based multi-tenant customer service system with AI-powered chat agents, JWT authentication, and Argon2 password hashing.

## 🏗️ Architecture

- **Admin Database**: `chatbot_system` with tenant management
- **Multi-tenant**: Each tenant has its own database and configuration
- **Authentication**: JWT tokens with Argon2 password hashing
- **AI Agents**: LangChain/LangGraph-based conversational AI
- **Configuration**: Dynamic collection names from tenant settings

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone and navigate to project
cd test_agent

# Install dependencies
uv install

# Set environment variables
cp .env.example .env
# Edit .env with your API keys and database URLs
```

### 2. Setup Database

```bash
# Run the setup script to create admin database and tenant data
cd src
python setup_admin_db.py
```

### 3. Start the API

```bash
# Start the FastAPI server
python main.py
```

The API will be available at: http://localhost:8000

## 📋 Test Accounts

| Username   | Password       | Role       | Access Level |
|------------|----------------|------------|--------------|
| admin      | admin123       | admin      | Full access  |
| supervisor | supervisor123  | supervisor | Limited admin|
| agent1     | agent123       | agent      | Basic access |

## 🔗 API Endpoints

### Authentication
- `POST /api/v1/login` - Login with username/password/tenant
- `GET /api/v1/verify_token` - Verify JWT token

### Chat
- `POST /api/v1/chat` - Chat with AI agent
- `GET /api/v1/chat/health` - Chat service health check

### System
- `GET /health` - API health check
- `GET /docs` - Interactive API documentation

## 🔑 Authentication Examples

### Login Request
```bash
curl -X POST "http://localhost:8000/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=admin&password=admin123&client_id=ambition-guru&scope="
```

### Login Response
```json
{
  "id": "user_id",
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "bearer",
  "username": "admin",
  "role": "admin",
  "tenant_id": "tenant_id_here",
  "tenant_label": "Ambition Guru Education",
  "tenant_slug": "ambition-guru",
  "expires_at": "2024-07-03T18:00:00"
}
```

## 💬 Chat Examples

### Basic Chat
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello! What courses do you offer?"}'
```

### Chat Response
```json
{
  "response": "Hello! We offer a wide range of courses...",
  "thread_id": "user_id_here",
  "user_id": "user_id_here"
}
```

## 🧪 Testing

### Run Comprehensive Tests
```bash
python test_api.py
```

### Manual Testing
1. **API Documentation**: http://localhost:8000/docs
2. **Health Check**: http://localhost:8000/health
3. **Login**: Use form data with `grant_type=password`
4. **Chat**: Use Bearer token authentication

## 🏢 Tenant Configuration

### Current Tenant
- **Name**: Ambition Guru
- **Slug**: `ambition-guru` (used as client_id)
- **Database**: `ambition_guru_db`
- **Domain**: `ambition-guru.com`

### Database Structure
```
chatbot_system/                 # Admin database
├── tenants                     # Tenant metadata
└── ...

ambition_guru_db/              # Tenant database
├── users                      # User accounts
├── settings                   # Configuration
├── invitations               # User invitations
└── ...
```

## ⚙️ Configuration

### Environment Variables
```bash
# Database
MONGO_URI=mongodb://localhost:27017

# API Keys
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_google_key

# JWT
SECRET_KEY=your_secret_key

# Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=optional_api_key
```

### Tenant Settings
Collection names and configurations are stored in tenant's `settings` collection:

```json
{
  "name": "env",
  "qdrant_config": {
    "info_collection": "information",
    "product_collection": "products",
    "qdrant_url": "http://localhost:6333"
  },
  "openai_config": {
    "embedding_model": "text-embedding-3-large",
    "embedding_dimensions": 1536
  }
}
```

## 🔧 Development

### Project Structure
```
src/
├── api/
│   ├── services/agent_v2/     # AI agents
│   └── v1/                    # API endpoints
├── core/                      # Core functionality
├── models/                    # Data models
├── utils/                     # Utilities
└── main.py                    # FastAPI app
```

### Key Features
- **Argon2 Password Hashing**: Secure password storage
- **JWT Authentication**: Stateless authentication
- **Multi-tenant Architecture**: Isolated tenant data
- **Dynamic Configuration**: No hardcoded collection names
- **AI Agent Integration**: LangChain/LangGraph agents
- **Conversation Continuity**: User ID as thread ID

## 🐛 Troubleshooting

### Common Issues

1. **Login Failed**: Check username/password and client_id
2. **Chat Not Working**: Verify JWT token in Authorization header
3. **Agent Errors**: Check API keys and vector database connection
4. **Database Issues**: Ensure MongoDB is running and accessible

### Swagger OAuth Issue
The Swagger UI OAuth button calls `/login` but our endpoint expects form data. Use the "Try it out" feature instead:

1. Go to `/docs`
2. Find `POST /api/v1/login`
3. Click "Try it out"
4. Fill in the form data:
   - `grant_type`: password
   - `username`: admin
   - `password`: admin123
   - `client_id`: ambition-guru
   - `scope`: (leave empty)

## 📚 API Documentation

Interactive documentation with examples: http://localhost:8000/docs

## 🤝 Contributing

1. Follow the existing code structure
2. Use Argon2 for password hashing
3. Fetch configurations from tenant settings
4. Maintain multi-tenant isolation
5. Add comprehensive tests

## 📄 License

This project is licensed under the MIT License.