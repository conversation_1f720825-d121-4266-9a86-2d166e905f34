# Multi-Tenant Customer Service API Examples

## Base URL
```
http://localhost:8000
```

## 1. Health Check
```bash
curl -X GET "http://localhost:8000/health"
```

## 2. Login (Get JWT Token)

### Admin User
```bash
curl -X POST "http://localhost:8000/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=admin&password=admin123&client_id=ambition-guru&scope="
```

### Supervisor User
```bash
curl -X POST "http://localhost:8000/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=supervisor&password=supervisor123&client_id=ambition-guru&scope="
```

### Agent User
```bash
curl -X POST "http://localhost:8000/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=agent1&password=agent123&client_id=ambition-guru&scope="
```

## 3. Verify Token
```bash
# Replace YOUR_TOKEN with the actual token from login response
curl -X GET "http://localhost:8000/api/v1/verify_token" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 4. Chat with Agent
```bash
# Replace YOUR_TOKEN with the actual token from login response
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello! What courses do you offer?"}'
```

## 5. Chat Conversation Examples

### Course Inquiry
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "I want to learn IELTS preparation"}'
```

### Booking Request
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "I want to book a demo class for IELTS"}'
```

### Troubleshooting
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "My app is not working properly"}'
```

## 6. Complete Login + Chat Example

### Step 1: Login and extract token
```bash
# Login and save response
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=admin&password=admin123&client_id=ambition-guru&scope=")

# Extract token (requires jq)
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.access_token')
echo "Token: $TOKEN"
```

### Step 2: Use token for chat
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello! What services do you provide?"}'
```

## Response Formats

### Login Response
```json
{
  "id": "user_id",
  "access_token": "jwt_token_here",
  "token_type": "bearer",
  "username": "admin",
  "role": "admin",
  "tenant_id": "tenant_id_here",
  "tenant_label": "Ambition Guru Education",
  "tenant_slug": "ambition-guru",
  "nav_permission": {...},
  "token_validity": {...},
  "expires_at": "2024-07-03T12:00:00"
}
```

### Chat Response
```json
{
  "response": "Hello! How can I help you today?",
  "thread_id": "user_id_here",
  "user_id": "user_id_here"
}
```

## Test Accounts

| Username   | Password       | Role       | Description |
|------------|----------------|------------|-------------|
| admin      | admin123       | admin      | Full access |
| supervisor | supervisor123  | supervisor | Limited admin access |
| agent1     | agent123       | agent      | Basic access |

## Tenant Information

- **Tenant Name**: Ambition Guru
- **Tenant Slug**: ambition-guru (used as client_id)
- **Database**: ambition_guru_db
- **Domain**: ambition-guru.com

## API Documentation

Interactive API documentation is available at:
```
http://localhost:8000/docs
```

## Notes

1. **Authentication**: Uses JWT tokens with Argon2 password hashing
2. **Multi-tenant**: Uses tenant slug (client_id) to identify tenant
3. **Thread Continuity**: User ID serves as thread ID for conversation continuity
4. **Agent Integration**: Chat endpoint integrates with agent_v2 system
5. **Configuration**: Collection names fetched from tenant settings (no hardcoding)
