"""
Setup script for admin database and multi-tenant system
Creates the chatbot_system admin database with tenants collection and dummy data
"""

import os
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime
from dotenv import load_dotenv
from argon2 import PasswordHasher

load_dotenv()

# Initialize password hasher
ph = PasswordHasher()

def setup_admin_database():
    """Setup the admin database with tenants collection"""
    
    # Connect to MongoDB
    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
    client = MongoClient(mongo_uri)
    
    # Create admin database
    admin_db = client["chatbot_system"]
    
    print("🔧 Setting up admin database: chatbot_system")
    
    # Create tenants collection
    tenants_collection = admin_db.tenants
    
    # Clear existing data (for fresh setup)
    tenants_collection.delete_many({})
    print("🗑️  Cleared existing tenant data")
    
    # Create single tenant
    dummy_tenants = [
        {
            "_id": ObjectId(),
            "name": "Ambition Guru",
            "slug": "ambition-guru",
            "database_name": "ambition_guru_db",
            "label": "Ambition Guru Education",
            "domain": "ambition-guru.com",
            "created_at": datetime.utcnow(),
            "status": "active",
            "settings": {
                "max_users": 100,
                "features": ["chat", "booking", "search"]
            }
        }
    ]
    
    # Insert dummy tenants
    result = tenants_collection.insert_many(dummy_tenants)
    print(f"✅ Created {len(result.inserted_ids)} tenants")
    
    # Print tenant information
    for tenant in dummy_tenants:
        print(f"   📋 {tenant['name']} (slug: {tenant['slug']}, db: {tenant['database_name']})")
    
    return dummy_tenants


def setup_tenant_databases(tenants):
    """Setup individual tenant databases with users and settings"""
    
    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
    client = MongoClient(mongo_uri)
    
    for tenant in tenants:
        db_name = tenant["database_name"]
        tenant_db = client[db_name]
        
        print(f"\n🔧 Setting up tenant database: {db_name}")
        
        # Clear existing data
        tenant_db.users.delete_many({})
        tenant_db.settings.delete_many({})
        tenant_db.invitations.delete_many({})
        
        # Create users collection with dummy users
        users_data = [
            {
                "_id": ObjectId(),
                "username": "admin",
                "hashed_password": ph.hash("admin123"),
                "role": "admin",
                "email": f"admin@{tenant['domain']}",
                "created_at": datetime.utcnow(),
                "status": "active"
            },
            {
                "_id": ObjectId(),
                "username": "supervisor",
                "hashed_password": ph.hash("supervisor123"),
                "role": "supervisor", 
                "email": f"supervisor@{tenant['domain']}",
                "created_at": datetime.utcnow(),
                "status": "active"
            },
            {
                "_id": ObjectId(),
                "username": "agent1",
                "hashed_password": ph.hash("agent123"),
                "role": "agent",
                "email": f"agent1@{tenant['domain']}",
                "created_at": datetime.utcnow(),
                "status": "active"
            }
        ]
        
        tenant_db.users.insert_many(users_data)
        print(f"   👥 Created {len(users_data)} users")
        
        # Create settings collection
        settings_data = [
            {
                "name": "token_validity",
                "days": 0,
                "hours": 6,
                "minutes": 0,
                "seconds": 0
            },
            {
                "name": "role_hierarchy",
                "roles": {
                    "admin": 3,
                    "supervisor": 2,
                    "agent": 1
                }
            },
            {
                "name": "nav_permission",
                "admin": {
                    "dashboard": True,
                    "users": True,
                    "chat": True,
                    "settings": True
                },
                "supervisor": {
                    "dashboard": True,
                    "users": True,
                    "chat": True,
                    "settings": False
                },
                "agent": {
                    "dashboard": True,
                    "users": False,
                    "chat": True,
                    "settings": False
                }
            },
            {
                "name": "env",
                "qdrant_config": {
                    "info_collection": "information",
                    "product_collection": "products",
                    "qdrant_url": "http://localhost:6333",
                    "qdrant_api_key": None
                },
                "minio_config": {
                    "access_key": "minioadmin",
                    "secret_key": "minioadmin",
                    "minio_url": "localhost:9000",
                    "bucket_name": f"{tenant['slug']}-bucket"
                },
                "openai_config": {
                    "embedding_model": "text-embedding-3-large",
                    "embedding_dimensions": 1536
                }
            }
        ]
        
        tenant_db.settings.insert_many(settings_data)
        print(f"   ⚙️  Created {len(settings_data)} settings")
        
        # Create indexes
        tenant_db.users.create_index("username", unique=True)
        tenant_db.users.create_index("email", unique=True)
        tenant_db.invitations.create_index("token", unique=True)
        
        print(f"   📊 Created database indexes")


def print_tenant_info(tenants):
    """Print tenant information for testing"""
    
    print("\n" + "="*60)
    print("🎯 MULTI-TENANT SYSTEM SETUP COMPLETE")
    print("="*60)
    
    print("\n📋 TENANT INFORMATION:")
    for tenant in tenants:
        print(f"\n🏢 {tenant['name']}")
        print(f"   Slug: {tenant['slug']}")
        print(f"   Database: {tenant['database_name']}")
        print(f"   Domain: {tenant['domain']}")
        print(f"   Tenant ID: {tenant['_id']}")
        
        print(f"\n   👥 Test Users:")
        print(f"   - admin / admin123 (role: admin)")
        print(f"   - supervisor / supervisor123 (role: supervisor)")
        print(f"   - agent1 / agent123 (role: agent)")
    
    print(f"\n🔗 API ENDPOINTS:")
    print(f"   Login: POST /api/v1/login")
    print(f"   Chat: POST /api/v1/chat")
    print(f"   Verify Token: GET /api/v1/verify_token")
    
    print(f"\n📝 LOGIN EXAMPLE:")
    print(f"   {{")
    print(f"     \"username\": \"admin\",")
    print(f"     \"password\": \"admin123\",")
    print(f"     \"client_id\": \"ambition-guru\"")
    print(f"   }}")


if __name__ == "__main__":
    print("🚀 Starting multi-tenant system setup...")
    
    # Setup admin database and tenants
    tenants = setup_admin_database()
    
    # Setup individual tenant databases
    setup_tenant_databases(tenants)
    
    # Print information
    print_tenant_info(tenants)
    
    print("\n✅ Multi-tenant system setup completed successfully!")
    print("🔧 You can now start the FastAPI application and test the endpoints.")
