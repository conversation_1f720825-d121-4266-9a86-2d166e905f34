"""
User Profile Service - Dynamic user profile management with MongoDB storage
"""

import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from bson import ObjectId

from core.database import get_db_from_tenant_id
from models.user_profile import UserProfile, UserProfileCreate, UserProfileUpdate, CourseInterest

logger = logging.getLogger(__name__)


class UserProfileService:
    """Service for managing user profiles with dynamic data storage"""
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection = self.db.user_profiles
        
        # Create indexes for better performance
        self._create_indexes()
        
    def _create_indexes(self):
        """Create database indexes for performance"""
        try:
            self.collection.create_index("user_id", unique=True)
            self.collection.create_index("tenant_id")
            self.collection.create_index([("user_id", 1), ("tenant_id", 1)])
            logger.info("✅ User profile indexes created")
        except Exception as e:
            logger.warning(f"Could not create indexes: {e}")
    
    def _calculate_completion_score(self, profile: UserProfile) -> float:
        """Calculate profile completion score (0-100)"""
        total_fields = 5  # name, email, phone, course_interests, preferences
        completed_fields = 0
        
        if profile.name:
            completed_fields += 1
        if profile.email:
            completed_fields += 1
        if profile.phone:
            completed_fields += 1
        if profile.course_interests:
            completed_fields += 1
        if profile.communication_preferences:
            completed_fields += 1
            
        return (completed_fields / total_fields) * 100
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile by user ID"""
        try:
            profile_data = self.collection.find_one({
                "user_id": user_id,
                "tenant_id": self.tenant_id
            })
            
            if profile_data:
                profile_data.pop("_id", None)
                profile = UserProfile(**profile_data)
                logger.info(f"✅ Retrieved profile for user: {user_id}")
                return profile
            else:
                logger.info(f"No profile found for user: {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            return None
    
    def create_user_profile(self, user_id: str, profile_data: UserProfileCreate) -> UserProfile:
        """Create a new user profile"""
        try:
            # Check if profile already exists
            existing_profile = self.get_user_profile(user_id)
            if existing_profile:
                logger.info(f"Profile already exists for user: {user_id}")
                return existing_profile
            
            # Create new profile
            profile = UserProfile(
                user_id=user_id,
                tenant_id=self.tenant_id,
                name=profile_data.name,
                email=profile_data.email,
                phone=profile_data.phone,
                preferred_language=profile_data.preferred_language,
                created_date=datetime.now(),
                last_updated=datetime.now()
            )
            
            # Calculate completion score
            profile.profile_completion_score = self._calculate_completion_score(profile)
            
            # Insert into database
            profile_dict = profile.dict(exclude={"id"})
            result = self.collection.insert_one(profile_dict)
            profile.id = str(result.inserted_id)
            
            logger.info(f"✅ Created profile for user: {user_id}")
            return profile
            
        except Exception as e:
            logger.error(f"Error creating user profile: {e}")
            raise
    
    def update_user_profile(self, user_id: str, update_data: UserProfileUpdate) -> Optional[UserProfile]:
        """Update user profile"""
        try:
            # Get existing profile
            existing_profile = self.get_user_profile(user_id)
            if not existing_profile:
                logger.warning(f"No profile found to update for user: {user_id}")
                return None
            
            # Prepare update data
            update_dict = {}
            if update_data.name is not None:
                update_dict["name"] = update_data.name
            if update_data.email is not None:
                update_dict["email"] = update_data.email
            if update_data.phone is not None:
                update_dict["phone"] = update_data.phone
            if update_data.preferred_language is not None:
                update_dict["preferred_language"] = update_data.preferred_language
            if update_data.communication_preferences is not None:
                update_dict["communication_preferences"] = update_data.communication_preferences
            
            update_dict["last_updated"] = datetime.now()
            
            # Update in database
            self.collection.update_one(
                {"user_id": user_id, "tenant_id": self.tenant_id},
                {"$set": update_dict}
            )
            
            # Get updated profile
            updated_profile = self.get_user_profile(user_id)
            if updated_profile:
                # Recalculate completion score
                updated_profile.profile_completion_score = self._calculate_completion_score(updated_profile)
                self.collection.update_one(
                    {"user_id": user_id, "tenant_id": self.tenant_id},
                    {"$set": {"profile_completion_score": updated_profile.profile_completion_score}}
                )
            
            logger.info(f"✅ Updated profile for user: {user_id}")
            return updated_profile
            
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
            return None
    
    def add_course_interest(self, user_id: str, course_code: str, course_name: str, 
                           interest_level: str = "interested", notes: str = None) -> bool:
        """Add course interest to user profile"""
        try:
            # Get or create profile
            profile = self.get_user_profile(user_id)
            if not profile:
                profile = self.create_user_profile(user_id, UserProfileCreate())
            
            # Check if interest already exists
            existing_interest = None
            for interest in profile.course_interests:
                if interest.course_code == course_code:
                    existing_interest = interest
                    break
            
            if existing_interest:
                # Update existing interest
                self.collection.update_one(
                    {
                        "user_id": user_id, 
                        "tenant_id": self.tenant_id,
                        "course_interests.course_code": course_code
                    },
                    {
                        "$set": {
                            "course_interests.$.interest_level": interest_level,
                            "course_interests.$.notes": notes,
                            "course_interests.$.added_date": datetime.now(),
                            "last_updated": datetime.now()
                        }
                    }
                )
                logger.info(f"✅ Updated course interest {course_code} for user: {user_id}")
            else:
                # Add new interest
                new_interest = CourseInterest(
                    course_code=course_code,
                    course_name=course_name,
                    interest_level=interest_level,
                    notes=notes,
                    added_date=datetime.now()
                )
                
                self.collection.update_one(
                    {"user_id": user_id, "tenant_id": self.tenant_id},
                    {
                        "$push": {"course_interests": new_interest.dict()},
                        "$set": {"last_updated": datetime.now()}
                    }
                )
                logger.info(f"✅ Added course interest {course_code} for user: {user_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding course interest: {e}")
            return False
    
    def remove_course_interest(self, user_id: str, course_code: str) -> bool:
        """Remove course interest from user profile"""
        try:
            self.collection.update_one(
                {"user_id": user_id, "tenant_id": self.tenant_id},
                {
                    "$pull": {"course_interests": {"course_code": course_code}},
                    "$set": {"last_updated": datetime.now()}
                }
            )
            
            logger.info(f"✅ Removed course interest {course_code} for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing course interest: {e}")
            return False
    
    def get_user_course_interests(self, user_id: str) -> List[CourseInterest]:
        """Get user's course interests"""
        try:
            profile = self.get_user_profile(user_id)
            if profile:
                return profile.course_interests
            return []
            
        except Exception as e:
            logger.error(f"Error getting course interests: {e}")
            return []
    
    def update_user_field(self, user_id: str, field_name: str, field_value: Any) -> bool:
        """Update a specific field in user profile"""
        try:
            # Validate field name to prevent injection
            allowed_fields = ["name", "email", "phone", "preferred_language", "communication_preferences"]
            if field_name not in allowed_fields:
                logger.warning(f"Invalid field name: {field_name}")
                return False
            
            self.collection.update_one(
                {"user_id": user_id, "tenant_id": self.tenant_id},
                {
                    "$set": {
                        field_name: field_value,
                        "last_updated": datetime.now()
                    }
                }
            )
            
            logger.info(f"✅ Updated {field_name} for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating user field: {e}")
            return False
