"""
Ticket Service - Dynamic ticket management with MongoDB storage
"""

import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from bson import ObjectId
import uuid

from core.database import get_db_from_tenant_id
from models.ticket import (
    TicketModel, TicketCreate, TicketUpdate, TicketResponse,
    TicketType, TicketStatus, TicketPriority
)

logger = logging.getLogger(__name__)


class TicketService:
    """Service for managing tickets with dynamic data storage"""
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection = self.db.tickets
        
        # Create indexes for better performance
        self._create_indexes()
        
    def _create_indexes(self):
        """Create database indexes for performance"""
        try:
            self.collection.create_index("ticket_number", unique=True)
            self.collection.create_index("user_id")
            self.collection.create_index("tenant_id")
            self.collection.create_index("ticket_type")
            self.collection.create_index("status")
            self.collection.create_index([("user_id", 1), ("tenant_id", 1)])
            self.collection.create_index([("ticket_type", 1), ("status", 1)])
            logger.info("✅ Ticket indexes created")
        except Exception as e:
            logger.warning(f"Could not create indexes: {e}")
    
    def _generate_ticket_number(self) -> str:
        """Generate unique ticket number"""
        try:
            # Generate a unique ticket number
            timestamp = datetime.now().strftime("%Y%m%d")
            unique_id = str(uuid.uuid4())[:8].upper()
            return f"TKT-{timestamp}-{unique_id}"
        except Exception as e:
            logger.error(f"Error generating ticket number: {e}")
            return f"TKT-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
    
    def create_ticket(self, ticket_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new ticket"""
        try:
            # Generate ticket number
            ticket_number = self._generate_ticket_number()
            
            # Create ticket document
            ticket_doc = {
                "ticket_number": ticket_number,
                "ticket_type": ticket_data.get("ticket_type", "general_inquiry"),
                "user_id": ticket_data.get("user_id"),
                "title": ticket_data.get("title", "General Request"),
                "description": ticket_data.get("description", ""),
                "priority": ticket_data.get("priority", "medium"),
                "status": "open",
                "tenant_id": self.tenant_id,
                "created_date": datetime.now(),
                "updated_date": datetime.now(),
                "metadata": ticket_data.get("metadata", {}),
                "tags": ticket_data.get("tags", [])
            }
            
            # Add optional fields if provided
            optional_fields = [
                "user_name", "user_email", "user_phone", 
                "course_code", "course_name", "time_slot", "thread_id"
            ]
            
            for field in optional_fields:
                if field in ticket_data:
                    ticket_doc[field] = ticket_data[field]
            
            # Insert into database
            result = self.collection.insert_one(ticket_doc)
            ticket_id = str(result.inserted_id)
            
            logger.info(f"✅ Ticket created: {ticket_number} (ID: {ticket_id})")
            
            return {
                "ticket_id": ticket_id,
                "ticket_number": ticket_number,
                "status": "open",
                "created_date": ticket_doc["created_date"]
            }
            
        except Exception as e:
            logger.error(f"❌ Error creating ticket: {e}")
            return None
    
    def get_ticket(self, ticket_id: str) -> Optional[TicketModel]:
        """Get ticket by ID"""
        try:
            ticket_data = self.collection.find_one({
                "_id": ObjectId(ticket_id),
                "tenant_id": self.tenant_id
            })
            
            if ticket_data:
                ticket_data["id"] = str(ticket_data.pop("_id"))
                ticket = TicketModel(**ticket_data)
                logger.info(f"✅ Retrieved ticket: {ticket.ticket_number}")
                return ticket
            else:
                logger.info(f"No ticket found with ID: {ticket_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting ticket: {e}")
            return None
    
    def get_ticket_by_number(self, ticket_number: str) -> Optional[TicketModel]:
        """Get ticket by ticket number"""
        try:
            ticket_data = self.collection.find_one({
                "ticket_number": ticket_number,
                "tenant_id": self.tenant_id
            })
            
            if ticket_data:
                ticket_data["id"] = str(ticket_data.pop("_id"))
                ticket = TicketModel(**ticket_data)
                logger.info(f"✅ Retrieved ticket by number: {ticket_number}")
                return ticket
            else:
                logger.info(f"No ticket found with number: {ticket_number}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting ticket by number: {e}")
            return None
    
    def update_ticket(self, ticket_id: str, update_data: TicketUpdate) -> Optional[TicketModel]:
        """Update ticket"""
        try:
            # Prepare update data
            update_dict = {"updated_date": datetime.now()}
            
            if update_data.status is not None:
                update_dict["status"] = update_data.status.value
                if update_data.status == TicketStatus.RESOLVED:
                    update_dict["resolved_date"] = datetime.now()
            
            if update_data.priority is not None:
                update_dict["priority"] = update_data.priority.value
            
            if update_data.assigned_to is not None:
                update_dict["assigned_to"] = update_data.assigned_to
            
            if update_data.metadata is not None:
                update_dict["metadata"] = update_data.metadata
            
            if update_data.tags is not None:
                update_dict["tags"] = update_data.tags
            
            # Update in database
            result = self.collection.update_one(
                {"_id": ObjectId(ticket_id), "tenant_id": self.tenant_id},
                {"$set": update_dict}
            )
            
            if result.modified_count > 0:
                logger.info(f"✅ Updated ticket: {ticket_id}")
                return self.get_ticket(ticket_id)
            else:
                logger.warning(f"No ticket updated with ID: {ticket_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error updating ticket: {e}")
            return None
    
    def get_user_tickets(self, user_id: str, limit: int = 50) -> List[TicketResponse]:
        """Get tickets for a user"""
        try:
            cursor = self.collection.find(
                {"user_id": user_id, "tenant_id": self.tenant_id}
            ).sort("created_date", -1).limit(limit)
            
            tickets = []
            for ticket_data in cursor:
                tickets.append(TicketResponse(
                    ticket_id=str(ticket_data["_id"]),
                    ticket_number=ticket_data["ticket_number"],
                    ticket_type=TicketType(ticket_data["ticket_type"]),
                    title=ticket_data["title"],
                    status=TicketStatus(ticket_data["status"]),
                    priority=TicketPriority(ticket_data["priority"]),
                    created_date=ticket_data["created_date"],
                    user_name=ticket_data.get("user_name"),
                    course_name=ticket_data.get("course_name")
                ))
            
            logger.info(f"📋 Retrieved {len(tickets)} tickets for user {user_id}")
            return tickets
            
        except Exception as e:
            logger.error(f"❌ Error getting user tickets: {e}")
            return []
    
    def get_tickets_by_status(self, status: str, limit: int = 100) -> List[TicketResponse]:
        """Get tickets by status"""
        try:
            cursor = self.collection.find(
                {"status": status, "tenant_id": self.tenant_id}
            ).sort("created_date", -1).limit(limit)
            
            tickets = []
            for ticket_data in cursor:
                tickets.append(TicketResponse(
                    ticket_id=str(ticket_data["_id"]),
                    ticket_number=ticket_data["ticket_number"],
                    ticket_type=TicketType(ticket_data["ticket_type"]),
                    title=ticket_data["title"],
                    status=TicketStatus(ticket_data["status"]),
                    priority=TicketPriority(ticket_data["priority"]),
                    created_date=ticket_data["created_date"],
                    user_name=ticket_data.get("user_name"),
                    course_name=ticket_data.get("course_name")
                ))
            
            logger.info(f"📋 Retrieved {len(tickets)} tickets with status: {status}")
            return tickets
            
        except Exception as e:
            logger.error(f"❌ Error getting tickets by status: {e}")
            return []
