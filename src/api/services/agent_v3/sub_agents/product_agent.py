"""
Product Management Sub-Agent
Handles course/product search, recommendations, and user interest tracking
NO HARDCODING - All logic is dynamic and LLM-driven
"""

import logging
from typing import Dict, List, Any

from models.user import UserTenantDB
from ..services.user_profile_service import UserProfileService

logger = logging.getLogger(__name__)


class ProductManagementAgent:
    """
    Sub-agent specialized for product/course management and recommendations
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the product management agent"""
        self.current_user = current_user
        self.agent_name = "Product Management Agent"
        
        # Initialize user profile service
        if current_user and current_user.tenant_id:
            self.user_profile_service = UserProfileService(current_user.tenant_id)
        else:
            self.user_profile_service = None
        
        logger.info("✅ Product Management Agent initialized")
    
    def _search_products(self, query: str) -> str:
        """Search for courses/products using vector store"""
        try:
            if self.current_user and self.current_user.vector_store_manager:
                result = self.current_user.vector_store_manager.search_products(query)
                logger.info(f"✅ Product search completed for: {query}")
                return result
            else:
                return "We offer various educational courses. What specific course interests you?"
        except Exception as e:
            logger.error(f"Product search failed: {e}")
            return "We have many programs available. Let me know what you're interested in."
    
    def handle_request(self, message: str, user_context: str = "") -> Dict[str, Any]:
        """
        Handle product management requests - completely dynamic, no hardcoding
        
        Args:
            message: User's message/query
            user_context: User context from profile and memory
            
        Returns:
            Dict with response, tools_used, and additional metadata
        """
        try:
            logger.info(f"🛍️ Product Agent processing: {message}")
            
            tools_used = []
            
            # Perform product search - let vector store handle all logic
            search_result = self._search_products(message)
            
            tools_used.append({
                "name": "search_products",
                "description": f"Searched for courses/products: {message}",
                "input": {"query": message},
                "success": True
            })
            
            # Return search result directly - no hardcoded processing
            response = search_result
            
            logger.info(f"✅ Product Agent completed request")
            
            return {
                "response": response,
                "tools_used": tools_used,
                "agent_type": "product"
            }
            
        except Exception as e:
            logger.error(f"Error in Product Agent: {e}")
            return {
                "response": "I'd be happy to help you find courses and programs. What type of course are you looking for?",
                "tools_used": [],
                "agent_type": "product",
                "error": str(e)
            }
    
    def get_capabilities(self) -> List[str]:
        """Get list of capabilities for this agent"""
        return [
            "Course and program search",
            "Educational product recommendations",
            "Course comparison and analysis",
            "User interest tracking",
            "Personalized course suggestions",
            "Product catalog navigation",
            "Course enrollment guidance",
            "Academic program information"
        ]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of the product management agent"""
        return {
            "agent_name": self.agent_name,
            "status": "active",
            "vector_store_available": (
                self.current_user and 
                self.current_user.vector_store_manager is not None
            ),
            "user_profile_service_available": self.user_profile_service is not None,
            "capabilities": self.get_capabilities()
        }
