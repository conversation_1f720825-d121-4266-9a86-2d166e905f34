"""
CTA (Call-to-Action) Sub-Agent
Handles bookings, tickets, and call-to-action operations
NO HARDCODING - All logic is dynamic and LLM-driven
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from models.user import UserTenantDB
from ..services.user_profile_service import UserProfileService
from ..services.ticket_service import TicketService

logger = logging.getLogger(__name__)


class CTAAgent:
    """
    Sub-agent specialized for call-to-action operations like bookings and tickets
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the CTA agent"""
        self.current_user = current_user
        self.agent_name = "CTA Agent"
        
        # Initialize services
        if current_user and current_user.tenant_id:
            self.user_profile_service = UserProfileService(current_user.tenant_id)
            self.ticket_service = TicketService(current_user.tenant_id)
        else:
            self.user_profile_service = None
            self.ticket_service = None
        
        logger.info("✅ CTA Agent initialized")
    
    def _create_booking_ticket(self, user_id: str, user_context: str, message: str) -> Dict[str, Any]:
        """Create a course booking ticket dynamically"""
        try:
            if not self.ticket_service:
                return {
                    "success": False,
                    "message": "Ticket service not available"
                }
            
            # Create a general booking ticket - let the ticket service handle details
            ticket_data = {
                "ticket_type": "course_booking",
                "title": "Course Booking Request",
                "description": f"User message: {message}",
                "priority": "medium",
                "user_id": user_id
            }
            
            # Add user context if available
            if user_context:
                ticket_data["metadata"] = {"user_context": user_context}
            
            ticket = self.ticket_service.create_ticket(ticket_data)
            
            if ticket:
                return {
                    "success": True,
                    "ticket_id": ticket.get("ticket_id"),
                    "ticket_number": ticket.get("ticket_number"),
                    "message": f"Booking request created with ticket #{ticket.get('ticket_number')}"
                }
            else:
                return {
                    "success": False,
                    "message": "Could not create booking ticket"
                }
                
        except Exception as e:
            logger.error(f"Error creating booking ticket: {e}")
            return {
                "success": False,
                "message": "Error processing booking request"
            }
    
    def _create_support_ticket(self, user_id: str, user_context: str, message: str) -> Dict[str, Any]:
        """Create a support ticket dynamically"""
        try:
            if not self.ticket_service:
                return {
                    "success": False,
                    "message": "Ticket service not available"
                }
            
            ticket_data = {
                "ticket_type": "support_ticket",
                "title": "Support Request",
                "description": f"User message: {message}",
                "priority": "medium",
                "user_id": user_id
            }
            
            if user_context:
                ticket_data["metadata"] = {"user_context": user_context}
            
            ticket = self.ticket_service.create_ticket(ticket_data)
            
            if ticket:
                return {
                    "success": True,
                    "ticket_id": ticket.get("ticket_id"),
                    "ticket_number": ticket.get("ticket_number"),
                    "message": f"Support ticket created with number #{ticket.get('ticket_number')}"
                }
            else:
                return {
                    "success": False,
                    "message": "Could not create support ticket"
                }
                
        except Exception as e:
            logger.error(f"Error creating support ticket: {e}")
            return {
                "success": False,
                "message": "Error processing support request"
            }
    
    def handle_request(self, message: str, user_context: str = "") -> Dict[str, Any]:
        """
        Handle CTA requests - completely dynamic, no hardcoding
        
        Args:
            message: User's message/query
            user_context: User context from profile and memory
            
        Returns:
            Dict with response, tools_used, and additional metadata
        """
        try:
            logger.info(f"📞 CTA Agent processing: {message}")
            
            tools_used = []
            response = ""
            
            if self.current_user:
                user_id = str(self.current_user.user.id)
                
                # Create a general inquiry ticket for all CTA requests
                # Let the ticket service and backend handle the specific logic
                ticket_result = self._create_support_ticket(user_id, user_context, message)
                
                if ticket_result.get("success"):
                    response = f"I've created a ticket for your request: {ticket_result.get('message')}"
                    response += "\n\nOur team will review your request and get back to you soon."
                    
                    tools_used.append({
                        "name": "create_ticket",
                        "description": f"Created ticket for user request",
                        "input": {"message": message},
                        "success": True,
                        "ticket_number": ticket_result.get("ticket_number")
                    })
                else:
                    response = "I'd be happy to help you with your request. Let me connect you with our team."
                    
            else:
                response = "I'd be happy to help you with bookings and requests. How can I assist you today?"
            
            logger.info(f"✅ CTA Agent completed request")
            
            return {
                "response": response,
                "tools_used": tools_used,
                "agent_type": "cta"
            }
            
        except Exception as e:
            logger.error(f"Error in CTA Agent: {e}")
            return {
                "response": "I'm here to help with bookings and requests. How can I assist you?",
                "tools_used": [],
                "agent_type": "cta",
                "error": str(e)
            }
    
    def get_capabilities(self) -> List[str]:
        """Get list of capabilities for this agent"""
        return [
            "Course booking requests",
            "Support ticket creation",
            "General inquiry handling",
            "Appointment scheduling",
            "Contact form processing",
            "Enrollment assistance",
            "Customer service requests",
            "Technical issue reporting"
        ]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of the CTA agent"""
        return {
            "agent_name": self.agent_name,
            "status": "active",
            "ticket_service_available": self.ticket_service is not None,
            "user_profile_service_available": self.user_profile_service is not None,
            "capabilities": self.get_capabilities()
        }
