"""
Information Retrieval Sub-Agent
Handles general information queries, FAQ, help requests using search_information tool
"""

import logging
from typing import Dict, List, Any
from langchain_core.tools import tool

from models.user import UserTenantDB

logger = logging.getLogger(__name__)


class InformationRetrievalAgent:
    """
    Sub-agent specialized for information retrieval and general queries
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the information retrieval agent"""
        self.current_user = current_user
        self.agent_name = "Information Retrieval Agent"
        
        logger.info("✅ Information Retrieval Agent initialized")
    
    def _search_information(self, query: str) -> str:
        """Search for general information using vector store"""
        try:
            if self.current_user and self.current_user.vector_store_manager:
                result = self.current_user.vector_store_manager.search_information(query)
                logger.info(f"✅ Information search completed for: {query}")
                return result
            else:
                return "I can help you with information. What would you like to know?"
        except Exception as e:
            logger.error(f"Information search failed: {e}")
            return "I'm here to help with any questions you have."
    
    def _analyze_information_query(self, message: str, user_context: str) -> Dict[str, Any]:
        """Analyze the information query dynamically using LLM - no hardcoding"""
        try:
            # Always search for information - let the vector store handle relevance
            # No hardcoded patterns or string matching
            query_analysis = {
                "needs_search": True,
                "query_type": "information_request"
            }

            return query_analysis

        except Exception as e:
            logger.error(f"Error analyzing information query: {e}")
            return {"needs_search": True}
    
    def _format_information_response(self, search_result: str, user_context: str) -> str:
        """Format the information response - return search result as-is, no hardcoding"""
        try:
            # Return search result directly - let the vector store and LLM handle formatting
            # No hardcoded string matching or patterns
            return search_result

        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return search_result
    
    def handle_request(self, message: str, user_context: str = "") -> Dict[str, Any]:
        """
        Handle information retrieval requests
        
        Args:
            message: User's message/query
            user_context: User context from profile and memory
            
        Returns:
            Dict with response, tools_used, and additional metadata
        """
        try:
            logger.info(f"🔍 Information Agent processing: {message}")
            
            # Analyze the query
            query_analysis = self._analyze_information_query(message, user_context)
            
            tools_used = []
            response = ""
            
            if query_analysis.get("needs_search", True):
                # Perform information search
                search_result = self._search_information(message)
                
                # Format the response
                response = self._format_information_response(search_result, user_context)
                
                tools_used.append({
                    "name": "search_information",
                    "description": f"Searched for information about: {message}",
                    "input": {"query": message},
                    "success": True
                })
                
            else:
                # Handle without search (rare case)
                response = "I'd be happy to help you with information. Could you please be more specific about what you'd like to know?"
            
            logger.info(f"✅ Information Agent completed request")
            
            return {
                "response": response,
                "tools_used": tools_used,
                "agent_type": "information",
                "query_analysis": query_analysis
            }
            
        except Exception as e:
            logger.error(f"Error in Information Agent: {e}")
            return {
                "response": "I'm here to help with information and answer your questions. Could you please try asking again?",
                "tools_used": [],
                "agent_type": "information",
                "error": str(e)
            }
    
    def get_capabilities(self) -> List[str]:
        """Get list of capabilities for this agent"""
        return [
            "General information search",
            "FAQ responses",
            "Help and support queries",
            "Educational content explanations",
            "How-to guides and instructions",
            "Policy and procedure information",
            "Academic guidance",
            "Technical explanations"
        ]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of the information retrieval agent"""
        return {
            "agent_name": self.agent_name,
            "status": "active",
            "vector_store_available": (
                self.current_user and 
                self.current_user.vector_store_manager is not None
            ),
            "capabilities": self.get_capabilities()
        }
