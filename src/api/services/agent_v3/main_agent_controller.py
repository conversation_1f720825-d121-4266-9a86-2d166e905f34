"""
Main Agent Controller - Orchestrates three specialized sub-agents
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import Chat<PERSON>rom<PERSON><PERSON>emplate

from models.user import UserTenantDB
from utils.production_memory_manager import get_production_memory_manager
from .sub_agents.information_retrieval_agent import InformationRetrievalAgent
from .sub_agents.product_agent import ProductManagementAgent
from .sub_agents.cta_agent import CTAAgent
from .services.user_profile_service import UserProfileService
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class AgentType(str, Enum):
    """Types of sub-agents available"""
    INFORMATION = "information"
    PRODUCT = "product"
    CTA = "cta"
    GENERAL = "general"


class MainAgentController:
    """
    Main Agent Controller that orchestrates three specialized sub-agents:
    1. Information Retrieval Agent - General information, FAQ, help
    2. Product Management Agent - Course search, recommendations, interests
    3. CTA Agent - Bookings, tickets, call-to-actions
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the main agent controller"""
        self.current_user = current_user
        
        # Single LLM instance for the main controller
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Initialize memory
        if current_user and current_user.tenant_id:
            self.memory = get_production_memory_manager(current_user.tenant_id, "gemini")
            self.user_profile_service = UserProfileService(current_user.tenant_id)
        else:
            self.memory = None
            self.user_profile_service = None
            
        # Initialize sub-agents
        self.info_agent = InformationRetrievalAgent(current_user)
        self.product_agent = ProductManagementAgent(current_user)
        self.cta_agent = CTAAgent(current_user)
        
        logger.info("✅ Main Agent Controller initialized with sub-agents")

    def _classify_intent(self, message: str) -> AgentType:
        """
        Classify user intent to determine which sub-agent should handle the request
        Uses LLM to dynamically determine intent - no hardcoding
        """
        try:
            classification_prompt = f"""
            Analyze the user message and classify the intent into one of these categories:

            1. INFORMATION - General questions, help requests, FAQ, how-to questions, explanations
            2. PRODUCT - Course search, product inquiries, recommendations, course details, educational programs
            3. CTA - Booking requests, scheduling, contact forms, tickets, appointments, enrollment actions
            4. GENERAL - Greetings, casual conversation, unclear intent

            User message: "{message}"

            Respond with only one word: INFORMATION, PRODUCT, CTA, or GENERAL
            """

            # Add timeout and max tokens to prevent hanging
            response = self.llm.invoke(
                [HumanMessage(content=classification_prompt)],
                config={"max_tokens": 10, "timeout": 10}
            )
            intent = response.content.strip().upper()

            # Map to enum values with fallback
            intent_mapping = {
                "INFORMATION": AgentType.INFORMATION,
                "PRODUCT": AgentType.PRODUCT,
                "CTA": AgentType.CTA,
                "GENERAL": AgentType.GENERAL
            }

            classified_intent = intent_mapping.get(intent, AgentType.GENERAL)
            logger.info(f"🎯 Intent classified as: {classified_intent.value}")
            return classified_intent

        except Exception as e:
            logger.error(f"Error classifying intent: {e}")
            return AgentType.GENERAL

    def _get_user_context(self) -> str:
        """Get user context from profile and memory"""
        try:
            context_parts = []
            
            # Get user profile if available
            if self.user_profile_service and self.current_user:
                user_id = str(self.current_user.user.id)
                profile = self.user_profile_service.get_user_profile(user_id)
                
                if profile:
                    if profile.name:
                        context_parts.append(f"User name: {profile.name}")
                    if profile.email:
                        context_parts.append(f"Email: {profile.email}")
                    if profile.phone:
                        context_parts.append(f"Phone: {profile.phone}")
                    if profile.course_interests:
                        interests = [interest.course_name for interest in profile.course_interests]
                        context_parts.append(f"Interested courses: {', '.join(interests)}")
            
            # Get recent memory context
            if self.memory and self.current_user:
                try:
                    user_id = str(self.current_user.user.id)
                    recent_memories = self.memory.search_memories(
                        user_id=user_id,
                        query="recent conversation context",
                        limit=3
                    )
                    if recent_memories:
                        memory_context = [mem.content for mem in recent_memories[:2]]
                        context_parts.extend(memory_context)
                except Exception as e:
                    logger.warning(f"Could not get memory context: {e}")
            
            return "\n".join(context_parts) if context_parts else ""
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return ""

    def _delegate_to_sub_agent(self, message: str, agent_type: AgentType, user_context: str) -> dict:
        """Delegate the message to the appropriate sub-agent"""
        try:
            if agent_type == AgentType.INFORMATION:
                return self.info_agent.handle_request(message, user_context)
            elif agent_type == AgentType.PRODUCT:
                return self.product_agent.handle_request(message, user_context)
            elif agent_type == AgentType.CTA:
                return self.cta_agent.handle_request(message, user_context)
            else:
                # Handle general conversation with main LLM
                return self._handle_general_conversation(message, user_context)
                
        except Exception as e:
            logger.error(f"Error delegating to sub-agent {agent_type}: {e}")
            return {
                "response": "I'm here to help! Could you please rephrase your question?",
                "tools_used": [],
                "agent_used": agent_type.value
            }

    def _handle_general_conversation(self, message: str, user_context: str) -> dict:
        """Handle general conversation that doesn't require specific sub-agents"""
        try:
            # Create a conversational prompt
            conversation_prompt = f"""
            You are a helpful assistant for Ambition Guru Education. 
            
            User Context:
            {user_context}
            
            Respond naturally to the user's message. If they're greeting you, greet them back.
            If they need specific help with courses, information, or booking, guide them appropriately.
            
            User message: {message}
            """
            
            response = self.llm.invoke([HumanMessage(content=conversation_prompt)])
            
            return {
                "response": response.content,
                "tools_used": [],
                "agent_used": "general"
            }
            
        except Exception as e:
            logger.error(f"Error in general conversation: {e}")
            return {
                "response": "Hello! I'm here to help you with courses, information, and bookings. How can I assist you today?",
                "tools_used": [],
                "agent_used": "general"
            }

    def chat(self, message: str, thread_id: str = None) -> dict:
        """Process chat message through the main agent controller"""
        try:
            # Get user context
            user_context = self._get_user_context()
            
            # Classify intent to determine which sub-agent to use
            agent_type = self._classify_intent(message)
            
            # Delegate to appropriate sub-agent
            result = self._delegate_to_sub_agent(message, agent_type, user_context)
            
            # Add agent type to result
            result["agent_used"] = agent_type.value
            
            # Save to memory
            if self.memory and self.current_user:
                try:
                    user_id = str(self.current_user.user.id)
                    chat_history = self.memory.get_chat_history(user_id)
                    chat_history.add_user_message(message)
                    chat_history.add_ai_message(result.get("response", ""))
                except Exception as e:
                    logger.warning(f"Could not save to memory: {e}")
            
            logger.info(f"✅ Chat processed by {agent_type.value} agent")
            return result
            
        except Exception as e:
            logger.error(f"Error in main agent chat: {e}")
            return {
                "response": "I'm here to help! How can I assist you today?",
                "tools_used": [],
                "agent_used": "error"
            }

    def get_agent_status(self) -> dict:
        """Get status of all sub-agents"""
        return {
            "main_controller": "active",
            "information_agent": "active" if self.info_agent else "inactive",
            "product_agent": "active" if self.product_agent else "inactive",
            "cta_agent": "active" if self.cta_agent else "inactive",
            "memory_available": self.memory is not None,
            "user_profile_available": self.user_profile_service is not None
        }
