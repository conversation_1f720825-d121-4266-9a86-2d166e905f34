"""
Comprehensive Test Suite for Agent V3 Architecture
Tests all agent interactions, user profile management, booking flows, ticket creation, and memory persistence
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

from models.user import User, UserTenantDB
from api.services.agent_v3_chat_service import AgentV3ChatService
from api.services.agent_v3.main_agent_controller import MainAgentController
from api.services.agent_v3.services.user_profile_service import UserProfileService
from api.services.agent_v3.services.ticket_service import TicketService
from models.user_profile import UserProfileCreate
from utils import setup_colored_logging

# Setup logging
setup_colored_logging()
logger = logging.getLogger(__name__)


class AgentV3TestSuite:
    """Comprehensive test suite for Agent V3 architecture"""
    
    def __init__(self):
        """Initialize test suite"""
        self.tenant_id = "676b8b8b8b8b8b8b8b8b8b8b"  # Test tenant ID
        self.user_id = "test_user_123"
        
        # Create mock user
        self.mock_user = User(
            _id=self.user_id,
            username="test_user",
            role="agent"
        )
        
        # Create mock UserTenantDB
        self.mock_user_tenant = UserTenantDB(
            tenant_id=self.tenant_id,
            tenant_database_name="test_db",
            slug="test-tenant",
            db=None,
            user=self.mock_user,
            async_db=None
        )
        
        # Initialize services
        self.chat_service = AgentV3ChatService()
        self.agent_controller = MainAgentController(self.mock_user_tenant)
        self.user_profile_service = UserProfileService(self.tenant_id)
        self.ticket_service = TicketService(self.tenant_id)
        
        logger.info("✅ Agent V3 Test Suite initialized")
    
    def test_agent_controller_initialization(self):
        """Test 1: Agent Controller Initialization"""
        logger.info("🧪 Test 1: Agent Controller Initialization")
        
        try:
            # Test agent status
            status = self.agent_controller.get_agent_status()
            
            assert status["main_controller"] == "active"
            assert "information_agent" in status
            assert "product_agent" in status
            assert "cta_agent" in status
            
            logger.info("✅ Test 1 PASSED: Agent controller initialized correctly")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 1 FAILED: {e}")
            return False
    
    def test_intent_classification(self):
        """Test 2: Intent Classification"""
        logger.info("🧪 Test 2: Intent Classification")
        
        test_messages = [
            ("What is the admission process?", "information"),
            ("I want to find courses about programming", "product"),
            ("I want to book a course", "cta"),
            ("Hello, how are you?", "general")
        ]
        
        try:
            for message, expected_type in test_messages:
                result = self.agent_controller.chat(message)
                agent_used = result.get("agent_used", "unknown")
                
                logger.info(f"Message: '{message}' -> Agent: {agent_used}")
                
                # Note: Since we're using LLM for classification, we can't assert exact matches
                # but we can verify that a valid agent was used
                assert agent_used in ["information", "product", "cta", "general"]
            
            logger.info("✅ Test 2 PASSED: Intent classification working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 2 FAILED: {e}")
            return False
    
    def test_user_profile_management(self):
        """Test 3: User Profile Management"""
        logger.info("🧪 Test 3: User Profile Management")
        
        try:
            # Create user profile
            profile_data = UserProfileCreate(
                name="Test User",
                email="<EMAIL>",
                phone="+1234567890",
                preferred_language="english"
            )
            
            profile = self.user_profile_service.create_user_profile(self.user_id, profile_data)
            assert profile is not None
            assert profile.name == "Test User"
            assert profile.email == "<EMAIL>"
            
            # Test adding course interest
            success = self.user_profile_service.add_course_interest(
                user_id=self.user_id,
                course_code="TEST-101",
                course_name="Test Course",
                interest_level="interested"
            )
            assert success
            
            # Test retrieving profile
            retrieved_profile = self.user_profile_service.get_user_profile(self.user_id)
            assert retrieved_profile is not None
            assert len(retrieved_profile.course_interests) > 0
            
            logger.info("✅ Test 3 PASSED: User profile management working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 3 FAILED: {e}")
            return False
    
    def test_ticket_creation(self):
        """Test 4: Ticket Creation and Management"""
        logger.info("🧪 Test 4: Ticket Creation and Management")
        
        try:
            # Test course booking ticket
            booking_ticket_data = {
                "ticket_type": "course_booking",
                "title": "Course Booking Request",
                "description": "User wants to book a programming course",
                "user_id": self.user_id,
                "user_name": "Test User",
                "user_email": "<EMAIL>",
                "course_code": "PROG-101",
                "course_name": "Programming Basics"
            }
            
            booking_ticket = self.ticket_service.create_ticket(booking_ticket_data)
            assert booking_ticket is not None
            assert "ticket_number" in booking_ticket
            
            # Test support ticket
            support_ticket_data = {
                "ticket_type": "support_ticket",
                "title": "Technical Issue",
                "description": "User having login problems",
                "user_id": self.user_id
            }
            
            support_ticket = self.ticket_service.create_ticket(support_ticket_data)
            assert support_ticket is not None
            
            # Test retrieving user tickets
            user_tickets = self.ticket_service.get_user_tickets(self.user_id)
            assert len(user_tickets) >= 2
            
            logger.info("✅ Test 4 PASSED: Ticket creation and management working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 4 FAILED: {e}")
            return False
    
    def test_chat_service_integration(self):
        """Test 5: Chat Service Integration"""
        logger.info("🧪 Test 5: Chat Service Integration")
        
        test_messages = [
            "Hello, I need help with courses",
            "What programming courses do you offer?",
            "I want to book a Python course"
        ]
        
        try:
            for message in test_messages:
                result = self.chat_service.chat(
                    message=message,
                    current_user=self.mock_user_tenant,
                    thread_id=self.user_id
                )
                
                assert "response" in result
                assert "tools_used" in result
                assert "agent_used" in result
                
                logger.info(f"Message: '{message}' -> Response length: {len(result['response'])}")
            
            logger.info("✅ Test 5 PASSED: Chat service integration working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 5 FAILED: {e}")
            return False
    
    def test_sub_agent_capabilities(self):
        """Test 6: Individual Sub-Agent Capabilities"""
        logger.info("🧪 Test 6: Individual Sub-Agent Capabilities")
        
        try:
            # Test Information Retrieval Agent
            info_result = self.agent_controller.info_agent.handle_request(
                "What are your admission requirements?",
                ""
            )
            assert "response" in info_result
            assert info_result["agent_type"] == "information"
            
            # Test Product Management Agent
            product_result = self.agent_controller.product_agent.handle_request(
                "Show me programming courses",
                ""
            )
            assert "response" in product_result
            assert product_result["agent_type"] == "product"
            
            # Test CTA Agent
            cta_result = self.agent_controller.cta_agent.handle_request(
                "I want to book a course",
                ""
            )
            assert "response" in cta_result
            assert cta_result["agent_type"] == "cta"
            
            logger.info("✅ Test 6 PASSED: All sub-agents working correctly")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test 6 FAILED: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests in the suite"""
        logger.info("🚀 Starting Agent V3 Comprehensive Test Suite")
        logger.info("=" * 60)
        
        tests = [
            self.test_agent_controller_initialization,
            self.test_intent_classification,
            self.test_user_profile_management,
            self.test_ticket_creation,
            self.test_chat_service_integration,
            self.test_sub_agent_capabilities
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"Test execution error: {e}")
                failed += 1
            
            logger.info("-" * 40)
        
        logger.info("=" * 60)
        logger.info(f"🏁 Test Suite Complete: {passed} PASSED, {failed} FAILED")
        
        if failed == 0:
            logger.info("🎉 ALL TESTS PASSED! Agent V3 architecture is working correctly.")
        else:
            logger.warning(f"⚠️  {failed} tests failed. Please review the implementation.")
        
        return failed == 0


def main():
    """Main test execution"""
    try:
        test_suite = AgentV3TestSuite()
        success = test_suite.run_all_tests()
        
        if success:
            logger.info("✅ Agent V3 architecture is ready for production!")
        else:
            logger.error("❌ Agent V3 architecture needs fixes before production.")
            
        return success
        
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
