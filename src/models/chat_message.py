from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId


class ToolUsedInMessage(BaseModel):
    """Tool used in a specific chat message"""
    name: str
    description: str
    input: Dict[str, Any] = {}
    output: str = ""


class ChatMessageModel(BaseModel):
    """Model for storing complete chat exchanges (user message + AI response + tools used)"""
    id: Optional[str] = Field(default=None, alias="_id")
    user_message: str
    ai_response: str
    tools_used: List[ToolUsedInMessage] = []
    timestamp: datetime = Field(default_factory=datetime.now)
    user_id: str
    session_id: str  # Usually same as user_id for permanent sessions
    tenant_id: str
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ChatMessageCreate(BaseModel):
    """Model for creating chat message records"""
    user_message: str
    ai_response: str
    tools_used: List[ToolUsedInMessage] = []


class ChatMessageResponse(BaseModel):
    """Model for chat message API responses"""
    id: str
    user_message: str
    ai_response: str
    tools_used: List[ToolUsedInMessage]
    timestamp: datetime


class ChatHistoryResponse(BaseModel):
    """Model for chat history responses"""
    session_id: str
    user_id: str
    messages: List[ChatMessageResponse]
    total_messages: int
    page: int = 1
    per_page: int = 50


class ChatStatsResponse(BaseModel):
    """Model for chat statistics"""
    total_messages: int
    messages_today: int
    most_used_tools: List[Dict[str, Any]]
    average_tools_per_message: float
