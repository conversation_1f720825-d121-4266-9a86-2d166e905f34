from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId


class CourseInterest(BaseModel):
    """Model for user course interests"""
    course_code: str
    course_name: str
    interest_level: str = "interested"  # interested, enrolled, completed
    added_date: datetime = Field(default_factory=datetime.now)
    notes: Optional[str] = None


class UserProfile(BaseModel):
    """Model for comprehensive user profiles"""
    id: Optional[str] = Field(default=None, alias="_id")
    user_id: str  # Reference to the main user ID
    tenant_id: str
    
    # Personal Information
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    
    # Course and Interest Management
    course_interests: List[CourseInterest] = []
    enrolled_courses: List[str] = []  # Course codes
    completed_courses: List[str] = []  # Course codes
    
    # Preferences and Context
    preferred_language: str = "english"
    communication_preferences: Dict[str, Any] = {}
    
    # Metadata
    created_date: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)
    profile_completion_score: float = 0.0  # 0-100 based on filled fields
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class UserProfileCreate(BaseModel):
    """Model for creating user profiles"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    preferred_language: str = "english"


class UserProfileUpdate(BaseModel):
    """Model for updating user profiles"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    preferred_language: Optional[str] = None
    communication_preferences: Optional[Dict[str, Any]] = None
