"""
Simple Test for Agent V3 Architecture - No Database Required
Tests the basic structure and imports of the Agent V3 system
"""

import sys
import os
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

from utils import setup_colored_logging

# Setup logging
setup_colored_logging()
logger = logging.getLogger(__name__)


def test_imports():
    """Test 1: Import all Agent V3 components"""
    logger.info("🧪 Test 1: Testing Agent V3 imports")
    
    try:
        # Test main imports
        from api.services.agent_v3_chat_service import AgentV3ChatService
        from api.services.agent_v3.main_agent_controller import MainAgentController, AgentType
        
        # Test sub-agent imports
        from api.services.agent_v3.sub_agents.information_retrieval_agent import InformationRetrievalAgent
        from api.services.agent_v3.sub_agents.product_agent import ProductManagementAgent
        from api.services.agent_v3.sub_agents.cta_agent import CTAAgent
        
        # Test service imports
        from api.services.agent_v3.services.user_profile_service import UserProfileService
        from api.services.agent_v3.services.ticket_service import TicketService
        
        # Test model imports
        from models.user_profile import User<PERSON>rofile, UserProfileCreate, CourseInterest
        from models.ticket import TicketModel, TicketType, TicketStatus, TicketPriority
        
        logger.info("✅ Test 1 PASSED: All imports successful")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Test 1 FAILED: Import error - {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Test 1 FAILED: Unexpected error - {e}")
        return False


def test_agent_initialization():
    """Test 2: Initialize Agent V3 components without database"""
    logger.info("🧪 Test 2: Testing Agent V3 initialization")
    
    try:
        # Test chat service initialization
        from api.services.agent_v3_chat_service import AgentV3ChatService
        chat_service = AgentV3ChatService()
        assert chat_service is not None
        
        # Test service info
        service_info = chat_service.get_service_info()
        assert service_info["service_name"] == "Agent V3 Chat Service"
        assert service_info["architecture"] == "main_controller_with_sub_agents"
        assert len(service_info["sub_agents"]) == 3
        
        logger.info("✅ Test 2 PASSED: Agent V3 initialization successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 2 FAILED: {e}")
        return False


def test_enum_definitions():
    """Test 3: Test enum definitions"""
    logger.info("🧪 Test 3: Testing enum definitions")
    
    try:
        from api.services.agent_v3.main_agent_controller import AgentType
        from models.ticket import TicketType, TicketStatus, TicketPriority
        
        # Test AgentType enum
        assert AgentType.INFORMATION == "information"
        assert AgentType.PRODUCT == "product"
        assert AgentType.CTA == "cta"
        assert AgentType.GENERAL == "general"
        
        # Test TicketType enum
        assert TicketType.COURSE_BOOKING == "course_booking"
        assert TicketType.SUPPORT_TICKET == "support_ticket"
        assert TicketType.GENERAL_INQUIRY == "general_inquiry"
        
        # Test TicketStatus enum
        assert TicketStatus.OPEN == "open"
        assert TicketStatus.IN_PROGRESS == "in_progress"
        assert TicketStatus.RESOLVED == "resolved"
        
        # Test TicketPriority enum
        assert TicketPriority.LOW == "low"
        assert TicketPriority.MEDIUM == "medium"
        assert TicketPriority.HIGH == "high"
        assert TicketPriority.URGENT == "urgent"
        
        logger.info("✅ Test 3 PASSED: All enum definitions correct")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 3 FAILED: {e}")
        return False


def test_model_creation():
    """Test 4: Test model creation without database"""
    logger.info("🧪 Test 4: Testing model creation")
    
    try:
        from models.user_profile import UserProfile, UserProfileCreate, CourseInterest
        from models.ticket import TicketModel, TicketCreate, TicketType, TicketStatus, TicketPriority
        from datetime import datetime
        
        # Test UserProfileCreate
        profile_create = UserProfileCreate(
            name="Test User",
            email="<EMAIL>",
            phone="+1234567890"
        )
        assert profile_create.name == "Test User"
        assert profile_create.email == "<EMAIL>"
        
        # Test CourseInterest
        course_interest = CourseInterest(
            course_code="TEST-101",
            course_name="Test Course",
            interest_level="interested"
        )
        assert course_interest.course_code == "TEST-101"
        assert course_interest.course_name == "Test Course"
        
        # Test TicketCreate
        ticket_create = TicketCreate(
            ticket_type=TicketType.COURSE_BOOKING,
            title="Test Booking",
            description="Test booking description",
            priority=TicketPriority.MEDIUM
        )
        assert ticket_create.ticket_type == TicketType.COURSE_BOOKING
        assert ticket_create.title == "Test Booking"
        
        logger.info("✅ Test 4 PASSED: Model creation successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 4 FAILED: {e}")
        return False


def test_agent_capabilities():
    """Test 5: Test agent capability definitions"""
    logger.info("🧪 Test 5: Testing agent capabilities")
    
    try:
        from api.services.agent_v3.sub_agents.information_retrieval_agent import InformationRetrievalAgent
        from api.services.agent_v3.sub_agents.product_agent import ProductManagementAgent
        from api.services.agent_v3.sub_agents.cta_agent import CTAAgent
        
        # Test Information Retrieval Agent capabilities
        info_agent = InformationRetrievalAgent()
        info_capabilities = info_agent.get_capabilities()
        assert len(info_capabilities) > 0
        assert "General information search" in info_capabilities
        
        # Test Product Management Agent capabilities
        product_agent = ProductManagementAgent()
        product_capabilities = product_agent.get_capabilities()
        assert len(product_capabilities) > 0
        assert "Course and program search" in product_capabilities
        
        # Test CTA Agent capabilities
        cta_agent = CTAAgent()
        cta_capabilities = cta_agent.get_capabilities()
        assert len(cta_capabilities) > 0
        assert "Course booking requests" in cta_capabilities
        
        logger.info("✅ Test 5 PASSED: Agent capabilities defined correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 5 FAILED: {e}")
        return False


def test_architecture_structure():
    """Test 6: Test overall architecture structure"""
    logger.info("🧪 Test 6: Testing architecture structure")
    
    try:
        # Test package structure
        import api.services.agent_v3
        import api.services.agent_v3.sub_agents
        import api.services.agent_v3.services
        
        # Test __all__ exports
        from api.services.agent_v3 import (
            MainAgentController,
            InformationRetrievalAgent,
            ProductManagementAgent,
            CTAAgent,
            UserProfileService,
            TicketService
        )
        
        # Verify all components are available
        assert MainAgentController is not None
        assert InformationRetrievalAgent is not None
        assert ProductManagementAgent is not None
        assert CTAAgent is not None
        assert UserProfileService is not None
        assert TicketService is not None
        
        logger.info("✅ Test 6 PASSED: Architecture structure is correct")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 6 FAILED: {e}")
        return False


def run_all_tests():
    """Run all simple tests"""
    logger.info("🚀 Starting Agent V3 Simple Test Suite")
    logger.info("=" * 60)
    
    tests = [
        test_imports,
        test_agent_initialization,
        test_enum_definitions,
        test_model_creation,
        test_agent_capabilities,
        test_architecture_structure
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test execution error: {e}")
            failed += 1
        
        logger.info("-" * 40)
    
    logger.info("=" * 60)
    logger.info(f"🏁 Simple Test Suite Complete: {passed} PASSED, {failed} FAILED")
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED! Agent V3 architecture structure is correct.")
        logger.info("📋 Architecture Summary:")
        logger.info("   • Main Agent Controller with intent classification")
        logger.info("   • Information Retrieval Sub-Agent")
        logger.info("   • Product Management Sub-Agent")
        logger.info("   • CTA (Call-to-Action) Sub-Agent")
        logger.info("   • User Profile Management Service")
        logger.info("   • Ticket Management Service")
        logger.info("   • No hardcoding - fully LLM-driven")
    else:
        logger.warning(f"⚠️  {failed} tests failed. Please review the implementation.")
    
    return failed == 0


def main():
    """Main test execution"""
    try:
        success = run_all_tests()
        
        if success:
            logger.info("✅ Agent V3 architecture is structurally sound!")
        else:
            logger.error("❌ Agent V3 architecture has structural issues.")
            
        return success
        
    except Exception as e:
        logger.error(f"Test suite execution failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
