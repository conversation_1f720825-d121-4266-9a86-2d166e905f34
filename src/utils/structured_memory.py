"""
Structured Long-Term Memory System using MongoDBSaver and Pydantic Models
Production-ready implementation following LangChain 2025 patterns
"""

import logging
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

logger = logging.getLogger(__name__)


class MemoryType(str, Enum):
    """Types of memories that can be stored"""
    PERSONAL_INFO = "personal_info"
    COURSE_INTEREST = "course_interest"
    LEARNING_GOAL = "learning_goal"
    PREFERENCE = "preference"
    FEEDBACK = "feedback"
    QUESTION = "question"
    BOOKING_HISTORY = "booking_history"
    INTERACTION = "interaction"


class ImportanceLevel(str, Enum):
    """Importance levels for memories"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class KnowledgeTriple(BaseModel):
    """Structured knowledge representation as subject-predicate-object triples"""
    subject: str = Field(..., description="The subject of the knowledge triple")
    predicate: str = Field(..., description="The relationship or action")
    object: str = Field(..., description="The object or target of the relationship")
    confidence: float = Field(default=1.0, ge=0.0, le=1.0, description="Confidence score")
    source: str = Field(default="conversation", description="Source of this knowledge")


class UserProfile(BaseModel):
    """Comprehensive user profile structure"""
    user_id: str = Field(..., description="Unique user identifier")
    
    # Personal Information
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    age: Optional[int] = None
    location: Optional[str] = None
    
    # Educational Background
    education_level: Optional[str] = None
    current_occupation: Optional[str] = None
    previous_education: List[str] = Field(default_factory=list)
    
    # Course Interests and Goals
    course_interests: List[str] = Field(default_factory=list)
    learning_goals: List[str] = Field(default_factory=list)
    preferred_learning_style: Optional[str] = None
    available_time: Optional[str] = None
    budget_range: Optional[str] = None
    
    # Preferences
    preferred_language: Optional[str] = None
    preferred_schedule: Optional[str] = None
    communication_preferences: List[str] = Field(default_factory=list)
    
    # Interaction History
    total_conversations: int = Field(default=0)
    last_interaction: Optional[datetime] = None
    favorite_topics: List[str] = Field(default_factory=list)
    
    # Booking History
    completed_courses: List[str] = Field(default_factory=list)
    current_enrollments: List[str] = Field(default_factory=list)
    cancelled_bookings: List[str] = Field(default_factory=list)
    
    # Feedback and Questions
    feedback_given: List[str] = Field(default_factory=list)
    common_questions: List[str] = Field(default_factory=list)
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    profile_completeness: float = Field(default=0.0, ge=0.0, le=1.0)
    
    def calculate_completeness(self) -> float:
        """Calculate profile completeness percentage"""
        total_fields = 0
        filled_fields = 0
        
        # Core fields (weighted more heavily)
        core_fields = [self.name, self.email, self.phone]
        for field in core_fields:
            total_fields += 3  # Weight core fields 3x
            if field:
                filled_fields += 3
        
        # Important fields
        important_fields = [
            self.education_level, self.current_occupation,
            self.preferred_language, self.location
        ]
        for field in important_fields:
            total_fields += 2  # Weight important fields 2x
            if field:
                filled_fields += 2
        
        # List fields
        list_fields = [
            self.course_interests, self.learning_goals,
            self.communication_preferences, self.completed_courses
        ]
        for field in list_fields:
            total_fields += 1
            if field:
                filled_fields += 1
        
        return filled_fields / total_fields if total_fields > 0 else 0.0
    
    def update_completeness(self):
        """Update the profile completeness score"""
        self.profile_completeness = self.calculate_completeness()
        self.updated_at = datetime.now()


class StructuredMemory(BaseModel):
    """Individual memory item with structured data"""
    memory_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User this memory belongs to")
    
    # Memory Content
    content: str = Field(..., description="The actual memory content")
    memory_type: MemoryType = Field(..., description="Type of memory")
    importance: ImportanceLevel = Field(default=ImportanceLevel.MEDIUM)
    
    # Structured Data
    knowledge_triples: List[KnowledgeTriple] = Field(default_factory=list)
    entities: List[str] = Field(default_factory=list, description="Named entities in the memory")
    keywords: List[str] = Field(default_factory=list, description="Key terms and concepts")
    
    # Context
    conversation_context: Optional[str] = None
    related_memories: List[str] = Field(default_factory=list, description="IDs of related memories")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    last_accessed: datetime = Field(default_factory=datetime.now)
    access_count: int = Field(default=0)
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0)
    
    # Embedding for semantic search (stored as list of floats)
    embedding: Optional[List[float]] = None
    
    def add_knowledge_triple(self, subject: str, predicate: str, obj: str, confidence: float = 1.0):
        """Add a knowledge triple to this memory"""
        triple = KnowledgeTriple(
            subject=subject,
            predicate=predicate,
            object=obj,
            confidence=confidence
        )
        self.knowledge_triples.append(triple)
    
    def increment_access(self):
        """Increment access count and update last accessed time"""
        self.access_count += 1
        self.last_accessed = datetime.now()


class ConversationSession(BaseModel):
    """Represents a conversation session"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User this session belongs to")
    thread_id: str = Field(..., description="LangGraph thread ID")
    
    # Session Info
    started_at: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)
    is_active: bool = Field(default=True)
    
    # Session Metadata
    total_messages: int = Field(default=0)
    session_type: str = Field(default="chat", description="Type of session (chat, booking, support)")
    session_summary: Optional[str] = None
    
    # Goals and Outcomes
    session_goals: List[str] = Field(default_factory=list)
    achieved_goals: List[str] = Field(default_factory=list)
    next_steps: List[str] = Field(default_factory=list)
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
    
    def add_message(self):
        """Increment message count and update activity"""
        self.total_messages += 1
        self.update_activity()


class MemorySearchResult(BaseModel):
    """Result from memory search operations"""
    memory: StructuredMemory
    relevance_score: float = Field(ge=0.0, le=1.0)
    search_type: str = Field(description="Type of search performed (semantic, keyword, etc.)")


class UserMemoryStats(BaseModel):
    """Statistics about a user's memory and interactions"""
    user_id: str
    total_memories: int = 0
    memories_by_type: Dict[MemoryType, int] = Field(default_factory=dict)
    total_sessions: int = 0
    active_sessions: int = 0
    profile_completeness: float = 0.0
    last_interaction: Optional[datetime] = None
    most_accessed_memory_type: Optional[MemoryType] = None
    average_session_length: float = 0.0


# Utility functions for memory management
def create_user_profile(user_id: str, **kwargs) -> UserProfile:
    """Create a new user profile with optional initial data"""
    profile = UserProfile(user_id=user_id, **kwargs)
    profile.update_completeness()
    return profile


def create_memory(
    user_id: str,
    content: str,
    memory_type: MemoryType,
    importance: ImportanceLevel = ImportanceLevel.MEDIUM,
    **kwargs
) -> StructuredMemory:
    """Create a new structured memory"""
    return StructuredMemory(
        user_id=user_id,
        content=content,
        memory_type=memory_type,
        importance=importance,
        **kwargs
    )


def extract_knowledge_triples(content: str, user_context: str = "") -> List[KnowledgeTriple]:
    """
    Extract knowledge triples from content using LLM
    This is a placeholder - should be implemented with actual LLM extraction
    """
    # This would be implemented with LLM-based extraction
    # For now, return empty list
    return []


def calculate_memory_importance(
    content: str,
    memory_type: MemoryType,
    user_context: str = ""
) -> ImportanceLevel:
    """
    Calculate the importance level of a memory based on content and context
    This is a placeholder - should be implemented with actual LLM analysis
    """
    # Simple heuristic - would be replaced with LLM-based analysis
    if memory_type in [MemoryType.PERSONAL_INFO, MemoryType.BOOKING_HISTORY]:
        return ImportanceLevel.HIGH
    elif memory_type in [MemoryType.COURSE_INTEREST, MemoryType.LEARNING_GOAL]:
        return ImportanceLevel.MEDIUM
    else:
        return ImportanceLevel.LOW
